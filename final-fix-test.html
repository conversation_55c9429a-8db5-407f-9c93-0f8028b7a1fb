<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复测试 - Streamly</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        header {
            background: rgba(15, 15, 35, 0.9);
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #4fc3f7;
        }

        /* 完全模拟Streamly的搜索结果弹窗 */
        .floatingMenu {
            background: rgba(15, 15, 35, 0.95) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid rgba(79, 195, 247, 0.3) !important;
            border-radius: 20px !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
            color: #e0e0e0 !important;
            position: fixed !important;
            width: 90vw !important;
            max-width: 800px !important;
            max-height: calc(100vh - 150px) !important;
            z-index: 10000 !important;
            padding: 20px;
            display: none;
        }

        .floatingMenu h2 {
            color: #4fc3f7 !important;
            border-bottom: 2px solid rgba(79, 195, 247, 0.3) !important;
            padding-bottom: 10px !important;
            cursor: move !important;
            user-select: none !important;
            transition: all 0.2s ease !important;
            margin: 0 0 15px 0;
            padding: 15px 8px !important;
            -webkit-touch-callout: none !important;
            -webkit-user-select: none !important;
            -khtml-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            touch-action: none !important;
        }

        .floatingMenu h2:hover {
            background: rgba(79, 195, 247, 0.15) !important;
            border-radius: 8px 8px 0 0 !important;
            transform: translateY(-1px) !important;
        }

        .floatingMenu.being-dragged h2 {
            background: rgba(79, 195, 247, 0.2) !important;
            border-radius: 8px 8px 0 0 !important;
            box-shadow: 0 2px 10px rgba(79, 195, 247, 0.3) !important;
        }

        .floatingMenuCloseButton {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 82, 82, 0.1) !important;
            border: 1px solid rgba(255, 82, 82, 0.3) !important;
            color: #ff5252 !important;
            border-radius: 8px !important;
            padding: 5px 10px !important;
            cursor: pointer;
            transition: all 0.3s ease !important;
        }

        .test-content {
            margin-top: 80px;
            padding: 20px;
            text-align: center;
        }

        .status-panel {
            position: fixed;
            bottom: 20px;
            left: 10px;
            right: 10px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-size: 11px;
            z-index: 3000;
            font-family: monospace;
        }

        .test-btn {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            padding: 12px 20px;
            margin: 10px;
            font-size: 14px;
        }

        .success {
            background: rgba(76, 175, 80, 0.2) !important;
            border: 2px solid #4caf50 !important;
        }

        .error {
            background: rgba(255, 82, 82, 0.2) !important;
            border: 2px solid #ff5252 !important;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 5px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <header>
        <div class="logo">🎉 最终修复测试 - 移动端弹窗拖拽</div>
    </header>

    <div class="test-content">
        <h3>📱 移动端弹窗拖拽最终修复验证</h3>
        <p>测试修复后的触摸拖拽功能</p>
        
        <button class="test-btn" onclick="showSearchWindow()">显示搜索结果弹窗</button>
        <button class="test-btn" onclick="hideSearchWindow()">隐藏弹窗</button>
        <button class="test-btn" onclick="resetTest()">重置测试</button>
        
        <div style="margin-top: 30px; font-size: 12px;">
            <h4>🔧 修复内容：</h4>
            <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                <li>✅ 只增强可见窗口，避免预处理</li>
                <li>✅ 触摸开始时零修改策略</li>
                <li>✅ 统一使用left/top坐标系统</li>
                <li>✅ 基于实际位置的拖拽计算</li>
                <li>✅ 完善的边界检查和验证</li>
            </ul>
            
            <div style="margin-top: 20px; padding: 15px; background: rgba(79, 195, 247, 0.1); border-radius: 8px;">
                <strong>测试步骤：</strong><br>
                1. 点击"显示搜索结果弹窗"<br>
                2. 用手指轻触蓝色标题栏<br>
                3. 观察弹窗是否跳跃<br>
                4. 尝试拖拽弹窗<br>
                5. 查看下方状态信息
            </div>
        </div>
    </div>

    <!-- 搜索结果弹窗 -->
    <div id="searchResultsWindow" class="floatingMenu">
        <h2>搜索结果:</h2>
        <div class="floatingMenuCloseButton" onclick="hideSearchWindow()">关闭</div>
        <div style="margin-top: 20px;">
            <p><strong>🎯 最终修复测试弹窗</strong></p>
            <p>请按住蓝色标题栏进行拖拽测试</p>
            
            <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                <strong>预期行为：</strong><br>
                • 触摸时弹窗不跳跃<br>
                • 拖拽平滑跟随手指<br>
                • 标题栏始终在手指下方<br>
                • 边界限制正常工作
            </div>
        </div>
    </div>

    <!-- 状态面板 -->
    <div class="status-panel">
        <div><strong>📊 修复验证状态</strong></div>
        <div class="info-grid">
            <div class="info-item">
                <strong>窗口增强:</strong><br>
                <span id="enhanceStatus">未检测</span>
            </div>
            <div class="info-item">
                <strong>触摸跳跃:</strong><br>
                <span id="jumpStatus">未测试</span>
            </div>
            <div class="info-item">
                <strong>拖拽状态:</strong><br>
                <span id="dragStatus">未开始</span>
            </div>
            <div class="info-item">
                <strong>坐标系统:</strong><br>
                <span id="coordStatus">未检查</span>
            </div>
        </div>
        <div style="margin-top: 10px; max-height: 80px; overflow-y: auto; font-size: 9px;">
            <div id="logOutput">等待操作...</div>
        </div>
    </div>

    <!-- 引入修复后的窗口管理器 -->
    <script src="res/js/stable-window-manager.js"></script>

    <script>
        let searchWindow = document.getElementById('searchResultsWindow');
        let touchStartPos = null;
        let logs = [];
        
        function showSearchWindow() {
            searchWindow.style.display = 'block';
            
            // 模拟搜索逻辑的居中定位
            setTimeout(() => {
                const rect = searchWindow.getBoundingClientRect();
                const headerHeight = 60;
                const availableHeight = window.innerHeight - headerHeight;
                const availableWidth = window.innerWidth;
                
                const centerX = (availableWidth - rect.width) / 2;
                const centerY = headerHeight + (availableHeight - rect.height) / 2;
                
                const constrainedX = Math.max(0, Math.min(centerX, availableWidth - rect.width));
                const constrainedY = Math.max(headerHeight, Math.min(centerY, headerHeight + availableHeight - rect.height));
                
                searchWindow.style.left = constrainedX + 'px';
                searchWindow.style.top = constrainedY + 'px';
                
                addLog(`弹窗居中显示: (${Math.round(constrainedX)}, ${Math.round(constrainedY)})`);
                updateStatus('coordStatus', '✅ left/top');
                
                // 检查是否被窗口管理器增强
                setTimeout(() => {
                    const isEnhanced = searchWindow.dataset.enhanced === 'true';
                    updateStatus('enhanceStatus', isEnhanced ? '✅ 已增强' : '⏳ 等待增强');
                }, 100);
            }, 50);
        }

        function hideSearchWindow() {
            searchWindow.style.display = 'none';
            addLog('弹窗已隐藏');
            resetStatus();
        }

        function resetTest() {
            hideSearchWindow();
            touchStartPos = null;
            logs = [];
            document.getElementById('logOutput').textContent = '测试已重置';
            resetStatus();
        }

        function updateStatus(elementId, text) {
            document.getElementById(elementId).textContent = text;
        }

        function resetStatus() {
            updateStatus('enhanceStatus', '未检测');
            updateStatus('jumpStatus', '未测试');
            updateStatus('dragStatus', '未开始');
            updateStatus('coordStatus', '未检查');
        }

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.unshift(`[${timestamp}] ${message}`);
            if (logs.length > 8) logs.pop();
            
            document.getElementById('logOutput').innerHTML = logs.join('<br>');
        }

        // 监听触摸事件
        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('#searchResultsWindow h2')) {
                const rect = searchWindow.getBoundingClientRect();
                touchStartPos = { x: rect.left, y: rect.top };
                
                addLog('📱 触摸开始');
                updateStatus('dragStatus', '🟡 触摸中');
                
                // 延迟检查跳跃
                setTimeout(() => {
                    const newRect = searchWindow.getBoundingClientRect();
                    const deltaX = Math.abs(newRect.left - touchStartPos.x);
                    const deltaY = Math.abs(newRect.top - touchStartPos.y);
                    
                    if (deltaX > 3 || deltaY > 3) {
                        updateStatus('jumpStatus', `❌ 跳跃 ${deltaX}px,${deltaY}px`);
                        searchWindow.classList.add('error');
                        addLog(`❌ 检测到跳跃: ΔX=${deltaX}px, ΔY=${deltaY}px`);
                    } else {
                        updateStatus('jumpStatus', '✅ 无跳跃');
                        searchWindow.classList.add('success');
                        addLog('✅ 触摸开始无跳跃');
                    }
                }, 50);
            }
        });

        document.addEventListener('touchmove', function(e) {
            if (touchStartPos && searchWindow.style.display !== 'none') {
                updateStatus('dragStatus', '🔵 拖拽中');
            }
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartPos) {
                updateStatus('dragStatus', '✅ 拖拽完成');
                addLog('📱 触摸结束');
                
                setTimeout(() => {
                    searchWindow.classList.remove('success', 'error');
                }, 2000);
            }
        });

        // 重写console.log来捕获窗口管理器日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && typeof args[0] === 'string' && args[0].includes('📱')) {
                addLog(args.join(' '));
            }
        };
    </script>
</body>
</html>
