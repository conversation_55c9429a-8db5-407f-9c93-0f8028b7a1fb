<!--
  Copyright 2018 LNFWebsite

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!doctype html>
<html>
<head>
  <title>Streamly</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Streamly - 现代化的在线视频流媒体播放器，支持 YouTube 视频搜索和播放，提供优雅的用户界面和流畅的播放体验。" />
  
  <!-- 现代化字体 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

  <!-- 外部样式库 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/FortAwesome/Font-Awesome@6/css/all.min.css" type="text/css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" type="text/css" />
  <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" type="text/css" />

  <!-- Icon Stuff -->
  <link rel="shortcut icon" id="favicon" href="res/img/favicon.png" type="image/x-icon" />
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <link rel="apple-touch-startup-image" href="res/img/icon.png">
  <link rel="apple-touch-icon" href="res/img/icon.png"/>
  <link rel="apple-touch-icon-precomposed" sizes="128x128" href="res/img/icon.png">
  <meta name="mobile-web-app-capable" content="yes">
  <link rel="shortcut icon" sizes="196x196" href="res/img/icon.png">
  <link rel="shortcut icon" sizes="128x128" href="res/img/icon.png">
  
  <!-- Meta tags for Open Graph Protocol -->
  <meta property="og:title" id="ogTitle" content="Streamly - 现代化视频流媒体播放器" />
  <meta property="og:url" content="https://lnfwebsite.github.io/Streamly" />
  <meta property="og:image" content="https://raw.githubusercontent.com/LNFWebsite/Streamly/master/res/img/icon.png" />
  
  <!-- CSS Stylesheet -->
  <link rel="stylesheet" href="res/css/styles.css" type="text/css" />
  <link rel="stylesheet" href="res/css/modern-theme.css" type="text/css" />
  <!--<link rel="stylesheet" href="res/css/sbs.css" type="text/css" />-->

  <!-- External scripts -->
  <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/clipboard@2/dist/clipboard.min.js"></script>
  <!--<script src="https://cdn.jsdelivr.net/gh/urin/jquery.balloon.js@1/jquery.balloon.min.js"></script>-->
</head>
<body>
  <div id="dataFramesContainer"></div>
  <header>
    <!-- 第一行：Logo + 搜索框 -->
    <div class="header-top-row">
      <h1 id="title">
        <a href="">
          <img id="logo" src="https://raw.githubusercontent.com/LNFWebsite/Streamly/master/res/img/logo/logo_streamly_color/logo_streamly_color_low_res.png" />
        </a>
      </h1>
      <div id="inputBoxContainer">
        <form action="javascript:input(0);" method="get" id="searchForm">
          <div class="search-input-wrapper">
            <input type="text" id="inputBox" placeholder="🎬 搜索视频、电影、纪录片或频道..." />
            <button type="submit" id="searchButton" class="search-btn" title="开始搜索">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </form>
        <div id="searchProgress">
          <div class="spinner">
            <div class="bounce1"></div>
            <div class="bounce2"></div>
            <div class="bounce3"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第二行：按钮区域 -->
    <div class="header-buttons-row">
      <button id="playlistManagerButton" onclick="togglePlaylistManager();" title="播放列表管理">📋</button>
      <span class="fas fa-columns headerButton" id="sbsButton" onclick="toggleSBS();" title="Toggle side-by-side view"></span>
      <span class="fas fa-cog headerButton" id="settingsButton" onclick="toggleMenu('settings');" title="Settings"></span>
      <span class="fas fa-exchange" id="stationIcon" onclick="disconnectStation();" title="Streamly Station is connected. Click to disconnect..."></span>
    </div>
  </header>
  <div id="main">
    <div id="dropShadow"></div>
    <div id="dropOverlay"><p>(Drop videos/playlists anywhere on screen)</p></div>
    <div id="searchResultsWindow" class="floatingMenu">
      <h2>搜索结果:</h2>
      <div class="floatingMenuCloseButton" onclick="toggleMenu('searchResults');">关闭</div>
      <div id="searchStatus" style="padding: 10px; font-style: italic; color: #666; display: none;"></div>
      <div id="searchResults" class="floatingMenuContents"></div>
    </div>
    <div id="settingsWindow" class="floatingMenu">
      <div class="floatingMenuCloseButton" onclick="toggleMenu('settings');">×</div>
      <h2>🎬 Streamly 设置</h2>
      <div class="floatingMenuContents">

        <!-- 播放设置 -->
        <div class="settings-section">
          <h3>🎵 播放设置</h3>
          <div class="setting-item">
            <label for="autoplayNext">自动播放下一个视频:</label>
            <input type="checkbox" id="autoplayNext" checked onchange="toggleAutoplayNext();">
          </div>
          <div class="setting-item">
            <label for="defaultVolume">默认音量:</label>
            <input type="range" id="defaultVolume" min="0" max="100" value="80" onchange="setDefaultVolume(this.value);">
            <span id="volumeValue">80%</span>
          </div>
          <div class="setting-item">
            <label for="playbackQuality">默认播放质量:</label>
            <select id="playbackQuality" onchange="setPlaybackQuality(this.value);">
              <option value="auto">自动</option>
              <option value="hd1080">1080p</option>
              <option value="hd720">720p</option>
              <option value="large">480p</option>
              <option value="medium">360p</option>
            </select>
          </div>
        </div>

        <!-- 界面设置 -->
        <div class="settings-section">
          <h3>🎨 界面设置</h3>
          <div class="setting-item">
            <label for="SBStoggle">分屏显示模式:</label>
            <input type="checkbox" id="SBStoggle" onchange="toggleSBS();">
          </div>
          <div class="setting-item">
            <label for="darkMode">深色主题:</label>
            <input type="checkbox" id="darkMode" checked onchange="toggleDarkMode();">
          </div>
          <div class="setting-item">
            <label for="showThumbnails">显示视频缩略图:</label>
            <input type="checkbox" id="showThumbnails" checked onchange="toggleThumbnails();">
          </div>
          <div class="setting-item">
            <label for="zenModeToggle">专注模式 (隐藏干扰元素):</label>
            <input type="checkbox" id="zenModeToggle" onchange="toggleZen();">
          </div>
        </div>

        <!-- 搜索设置 -->
        <div class="settings-section">
          <h3>🔍 搜索设置</h3>
          <div class="setting-item">
            <label for="searchClose">添加视频后自动关闭搜索窗口:</label>
            <input type="checkbox" id="searchClose" onchange="toggleSearchClose();">
            <small>提示: 按 Shift+\ 可临时反转此设置</small>
          </div>
          <div class="setting-item">
            <label for="searchResults">搜索结果数量:</label>
            <select id="searchResults" onchange="setSearchResultsCount(this.value);">
              <option value="10">10个结果</option>
              <option value="20" selected>20个结果</option>
              <option value="30">30个结果</option>
              <option value="50">50个结果</option>
            </select>
          </div>
          <div class="setting-item">
            <label for="safeSearch">安全搜索:</label>
            <select id="safeSearch" onchange="setSafeSearch(this.value);">
              <option value="moderate" selected>中等</option>
              <option value="strict">严格</option>
              <option value="none">关闭</option>
            </select>
          </div>
        </div>

        <!-- 播放列表设置 -->
        <div class="settings-section">
          <h3>📋 播放列表设置</h3>
          <div class="setting-item">
            <label for="autoplayListOverride">使用 YouTube 混合播放列表作为电台:</label>
            <input type="checkbox" id="autoplayListOverride" onchange="toggleAutoplayListOverride();">
          </div>
          <div class="setting-item">
            <label for="shuffleMode">默认随机播放:</label>
            <input type="checkbox" id="shuffleMode" onchange="toggleShuffleMode();">
          </div>
          <div class="setting-item">
            <label for="repeatMode">默认重复播放:</label>
            <select id="repeatMode" onchange="setRepeatMode(this.value);">
              <option value="none">不重复</option>
              <option value="one">单曲循环</option>
              <option value="all">列表循环</option>
            </select>
          </div>
        </div>

        <!-- 高级设置 -->
        <div class="settings-section">
          <h3>⚙️ 高级设置</h3>
          <div class="setting-item">
            <label for="preloadVideos">预加载视频:</label>
            <input type="checkbox" id="preloadVideos" checked onchange="togglePreloadVideos();">
            <small>提高播放流畅度，但会增加数据使用量</small>
          </div>
          <div class="setting-item">
            <label for="keyboardShortcuts">启用键盘快捷键:</label>
            <input type="checkbox" id="keyboardShortcuts" checked onchange="toggleKeyboardShortcuts();">
          </div>
          <div class="setting-item">
            <label for="analyticsEnabled">使用统计 (匿名):</label>
            <input type="checkbox" id="analyticsEnabled" onchange="toggleAnalytics();">
            <small>帮助改进 Streamly</small>
          </div>
        </div>

        <!-- 数据管理 -->
        <div class="settings-section">
          <h3>💾 数据管理</h3>
          <div class="setting-item">
            <button class="settings-button" onclick="exportSettings();">导出设置</button>
            <button class="settings-button" onclick="importSettings();">导入设置</button>
          </div>
          <div class="setting-item">
            <button class="settings-button" onclick="clearCache();">清除缓存</button>
            <button class="settings-button danger" onclick="resetSettings();">重置所有设置</button>
          </div>
        </div>

        <!-- 关于信息 -->
        <div class="settings-section">
          <h3>ℹ️ 关于 Streamly</h3>
          <div class="about-info">
            <p><strong>版本:</strong> 2.0.0</p>
            <p><strong>构建日期:</strong> <span id="buildDate">2024-01-01</span></p>
            <p><strong>开源项目:</strong> <a href="https://github.com/tonyhu2006/Streamly" target="_blank">GitHub</a></p>
            <p><strong>许可证:</strong> Apache 2.0</p>
          </div>
        </div>

      </div>
    </div>

    <!-- 播放列表管理窗口 -->
    <div id="playlistManagerWindow" class="floatingMenu" style="display: none;">
      <div class="floatingMenuCloseButton" onclick="togglePlaylistManager();">×</div>
      <h2>📋 播放列表管理</h2>
      <div class="floatingMenuContents">

        <!-- 当前状态 -->
        <div class="playlist-section">
          <h3>当前状态</h3>
          <div class="current-status">
            <div class="status-item">
              <span class="status-label">当前模式:</span>
              <span id="currentModeDisplay">临时播放队列</span>
            </div>
            <div class="status-item">
              <span class="status-label">队列中视频:</span>
              <span id="currentQueueCount">0</span>
            </div>
            <div class="status-item">
              <span class="status-label">本地播放列表:</span>
              <span id="localPlaylistCount">0</span>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="playlist-section">
          <h3>快速操作</h3>
          <div class="quick-actions">
            <button class="action-button" onclick="saveCurrentQueueAsPlaylist();">
              <i class="fas fa-save"></i> 保存当前队列
            </button>
            <button class="action-button" onclick="showCreatePlaylistDialog();">
              <i class="fas fa-plus"></i> 创建新播放列表
            </button>
            <button class="action-button secondary" onclick="switchPlaylist('temp');">
              <i class="fas fa-list"></i> 切换到临时队列
            </button>
          </div>
        </div>

        <!-- 本地播放列表 -->
        <div class="playlist-section">
          <h3>本地播放列表</h3>
          <div id="localPlaylistsList" class="playlists-container">
            <!-- 本地播放列表将在这里动态生成 -->
          </div>
        </div>

        <!-- 临时播放列表（原有功能） -->
        <div class="playlist-section">
          <h3>临时播放列表</h3>
          <div class="create-playlist-form">
            <input type="text" id="newPlaylistName" placeholder="输入播放列表名称..." maxlength="50">
            <textarea id="newPlaylistDescription" placeholder="描述（可选）..." rows="2" maxlength="200"></textarea>
            <button class="action-button" onclick="createNewPlaylist();">
              <i class="fas fa-plus"></i> 创建临时播放列表
            </button>
          </div>

          <div id="playlistsList" class="playlists-container">
            <!-- 临时播放列表将在这里动态生成 -->
          </div>
        </div>

      </div>
    </div>

    <!-- 播放列表选择弹窗 -->
    <div id="playlistSelectorModal" class="modal" style="display: none;" onclick="if(event.target === this) { closePlaylistSelector(); }">
      <div class="modal-content" onclick="event.stopPropagation();">
        <div class="modal-header">
          <h3>选择播放列表</h3>
          <button class="modal-close" onclick="closePlaylistSelector();">×</button>
        </div>
        <div class="modal-body">
          <div id="playlistSelectorList" class="playlist-selector-list">
            <!-- 播放列表选项将在这里生成 -->
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-button secondary" onclick="closePlaylistSelector();">取消</button>
        </div>
      </div>
    </div>
    <form action="javascript:input(2);" method="get">
      <div id="playlistSelector" class="playlist-selector">
        <select id="currentPlaylistSelect" onchange="switchPlaylist(this.value);" title="选择播放列表">
          <option value="temp">📝 临时播放队列</option>
        </select>
        <button id="newPlaylistBtn" onclick="showCreatePlaylistDialog();" title="创建新播放列表">➕</button>
        <button id="saveQueueBtn" onclick="saveCurrentQueueAsPlaylist();" title="保存当前队列为播放列表">💾</button>
        <button id="managePlaylistsBtn" onclick="togglePlaylistManager();" title="管理播放列表">📋</button>
      </div>
    </form>

    <!-- 欢迎消息 -->
    <div id="welcomeMessage" class="welcome-message" style="display: none;">
      <h2>🎬 欢迎使用 Streamly 2.0</h2>
      <p>全新的服务器端视频流媒体播放器</p>
      <p>✨ 在上方搜索框中输入视频名、频道或关键词开始您的观影之旅</p>
      <p>🎯 支持智能搜索、高质量播放和播放列表管理</p>
      <p>🚀 无需外部浏览器，享受纯净的视频体验</p>
    </div>

    <table id="videosTable"></table>
    <div id="links">
      <a href="/guide.html">使用指南</a> |
      <a href="https://github.com/LNFWebsite/Streamly/wiki/Playlists" target="_blank">播放列表帮助</a> |
      <a href="https://lnfwebsite.github.io/Streamly-Import/" target="_blank">导入播放列表</a> |
      <a href="https://www.reddit.com/r/StreamlyReddit/" target="_blank">StreamlyReddit</a>
      <br>
      <span style="color:#4CAF50;">✓ 服务器端视频搜索和播放功能已启用</span> |
      <span style="color:#2196F3;">支持内置视频流媒体播放，无需外部浏览器</span>
    </div>

    <!-- 快速创建播放列表对话框 -->
    <div id="quickCreatePlaylistModal" class="modal" style="display: none;" onclick="if(event.target === this) { closeQuickCreateDialog(); }">
      <div class="modal-content" onclick="event.stopPropagation();">
        <div class="modal-header">
          <h3>📋 创建新播放列表</h3>
          <button class="modal-close" onclick="closeQuickCreateDialog();">×</button>
        </div>
        <div class="modal-body">
          <div class="quick-create-form">
            <input type="text" id="quickPlaylistName" placeholder="播放列表名称..." maxlength="50">
            <textarea id="quickPlaylistDescription" placeholder="描述（可选）..." rows="2" maxlength="200"></textarea>
          </div>

          <!-- 存储信息区域 -->
          <div class="storage-info-section">
            <div class="storage-info-header" onclick="toggleStorageInfo();">
              <h4>📍 存储位置信息</h4>
              <button type="button" class="info-toggle-btn">
                <i class="fas fa-chevron-down"></i>
              </button>
            </div>

            <div id="storageInfoContent" class="storage-info-content" style="display: none;">
              <!-- 存储路径信息 -->
              <div class="storage-path-info">
                <h5>💾 本地存储路径</h5>
                <div class="path-display">
                  <code id="storagePath">检测中...</code>
                  <button type="button" class="copy-path-btn" onclick="copyStoragePath();" title="复制路径">
                    <i class="fas fa-copy"></i>
                  </button>
                </div>
                <div class="storage-key-info">
                  <small><strong>存储键名:</strong> <code>streamly-local-playlists</code></small>
                </div>
              </div>

              <!-- 存储限制信息 -->
              <div class="storage-limits">
                <h5>📊 存储限制</h5>
                <ul>
                  <li><strong>localStorage容量限制:</strong> 通常为 5-10MB</li>
                  <li><strong>数据持久性:</strong> 除非用户手动清除或使用隐私模式，否则数据永久保存</li>
                  <li><strong>域名隔离:</strong> 只能在相同域名下访问数据</li>
                </ul>
              </div>

              <!-- 隐私和安全信息 -->
              <div class="privacy-security">
                <h5>🔒 隐私和安全</h5>
                <ul class="security-list">
                  <li class="security-good">✅ 数据完全存储在用户本地浏览器中</li>
                  <li class="security-good">✅ 不会上传到任何服务器</li>
                  <li class="security-good">✅ 只有当前网站可以访问这些数据</li>
                  <li class="security-warning">⚠️ 清除浏览器数据会删除所有播放列表</li>
                  <li class="security-warning">⚠️ 隐私模式下的数据在关闭浏览器后会丢失</li>
                </ul>
              </div>

              <!-- 备份建议 -->
              <div class="backup-suggestion">
                <h5>💾 备份建议</h5>
                <p>建议定期导出播放列表数据进行备份，可通过浏览器开发者工具或应用内导出功能实现。</p>
                <button type="button" class="backup-btn" onclick="showBackupInstructions();">
                  <i class="fas fa-download"></i> 查看备份方法
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-button" onclick="createQuickPlaylist();">
            <i class="fas fa-plus"></i> 创建播放列表
          </button>
          <button class="action-button secondary" onclick="closeQuickCreateDialog();">取消</button>
        </div>
      </div>
    </div>
  </div>
  <footer>
    <div id="previousVideo" onclick="backVideo();" title="Back">
      <div class="videoNameContainer">
        <p class="videoName"></p>
      </div>
      <div class="videoImageContainer">
        <div class="videoImageWrapper">
          <img class="videoImage" src="" />
          <span class="fas fa-backward"></span>
          <p class="videoTime"></p>
        </div>
      </div>
    </div>
    <div id="playlistInterface">
      <div id="youtubeContainer">
        <span id="remotePauseIcon" class="fas fa-play"></span>
        <div id="playerStatus" style="position: absolute; top: 5px; right: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; z-index: 1000; display: none;"></div>
        <video id="videoPlayer" controls preload="metadata" style="width: 100%; height: 100%; display: none;">
          <source id="videoSource" src="" type="video/mp4">
          您的浏览器不支持 HTML5 视频播放。
        </video>
        <iframe id="youtube" src="" allowfullscreen style="display: none;"></iframe>
      </div>
      <div id="currentVideoTiming">
        <span id="currentTime">0:00</span>
        <span id="progressContainer">
          <span id="progress" style="width:0%;"></span>
        </span>
        <span id="videoTime">0:00</span>
      </div>
    </div>
    <div id="nextVideo" onclick="forwardVideo();" title="Forward">
      <div class="videoImageContainer">
        <div class="videoImageWrapper">
          <img class="videoImage" src="" />
          <span class="fas fa-forward"></span>
          <p class="videoTime"></p>
        </div>
      </div>
      <div class="videoNameContainer">
        <p class="videoName"></p>
      </div>
    </div>
    <div id="settings">
      <span class="fas fa-arrow-circle-right" onclick="playlistFeatures.playNext();" title="Added videos will play next"></span>
      <span class="fas fa-random" onclick="playlistFeatures.shuffle();" title="Playlist shuffle"></span>
      <span class="fas fa-redo-alt" onclick="playlistFeatures.repeat();" title="Playlist repeat"></span>
      <span class="fas fa-rss" onclick="playlistFeatures.autoplay();" title="Streamly Radio"></span>
      <span class="fab fa-reddit" id="ad" onclick="shareOnReddit();" title="Share playlist on Reddit"></span>
    </div>
  </footer>
  <!----
  <div id="dialog"><p>Hey everyone, unfortunately YouTube has sealed off one of their data sources which has rendered quick search and in-box search unusable. Right now I've switched back to pop-up search until I can find a better solution. Rest assured your playlists always were and continue to be safe. I apologize for this inconvenience.</p></div>
  <script>
  //this is a standard dialog box for jquery that can be used to non-annoyingly get messages to users on-load
  $("#dialog").dialog();
  </script>
  ---->
  <script src="res/js/front/initialize.js"></script>
  <script src="res/js/front/youtube.js"></script>
  <script src="res/js/front/videoplayer.js"></script>
  <script src="res/js/front/share.js"></script>
  <script src="res/js/front/events.js"></script>

  <script src="res/js/back/global.js"></script>
  <script src="res/js/back/base.js"></script>
  <script src="res/js/back/playlistBack.js"></script>
  <script src="res/js/back/playlistFront.js"></script>
  <script src="res/js/back/data.js"></script>
  <script src="res/js/back/progress.js"></script>
  <script src="res/js/back/input.js"></script>
  <script src="res/js/back/search.js"></script>
  <script src="res/js/back/radio.js"></script>
  <script src="res/js/back/station.js"></script>
  <script src="res/js/back/toggles.js"></script>

  <!-- 窗口管理器 - 提供弹窗拖拽和缩放功能 -->
  <script src="res/js/stable-window-manager.js"></script>

  <!--
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/front/initialize.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/front/youtube.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/front/share.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/front/events.min.js"></script>
  
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/global.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/base.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/playlistBack.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/playlistFront.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/data.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/progress.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/input.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/search.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/radio.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/station.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/LNFWebsite/Streamly@gh-pages/res/js/back/toggles.min.js"></script>
  -->
  <!-- Start of StatCounter Code for Default Guide -->
  <script type="text/javascript">
  var sc_project=10543623;
  var sc_invisible=1;
  var sc_security="4c623aaf";
  </script>
  <script type="text/javascript"
  src="https://www.statcounter.com/counter/counter.js"
  async></script>
  <noscript><div class="statcounter"><a title="Web Analytics"
  href="http://statcounter.com/" target="_blank"><img
  class="statcounter"
  src="//c.statcounter.com/10543623/0/4c623aaf/1/" alt="Web
  Analytics"></a></div></noscript>
  <!-- End of StatCounter Code for Default Guide -->

  <!-- 背景选择器 -->
  <script src="res/js/background-selector.js" type="text/javascript"></script>

  <!-- 设置管理器 -->
  <script src="res/js/settings-manager.js" type="text/javascript"></script>

  <!-- 播放列表管理器 -->
  <script src="res/js/playlist-manager.js" type="text/javascript"></script>

  <!-- 本地播放列表管理器 -->
  <script src="res/js/local-playlist-manager.js" type="text/javascript"></script>

  <!-- 稳定窗口管理器 -->
  <script src="res/js/stable-window-manager.js" type="text/javascript"></script>

  <!-- 现代化界面增强脚本 -->
  <script>
    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 显示欢迎消息（如果播放列表为空）
      function checkPlaylistEmpty() {
        const table = document.getElementById('videosTable');
        const welcomeMessage = document.getElementById('welcomeMessage');

        if (table && welcomeMessage) {
          if (table.children.length === 0) {
            welcomeMessage.style.display = 'block';
            welcomeMessage.classList.add('animate__animated', 'animate__fadeInUp');
          } else {
            welcomeMessage.style.display = 'none';
          }
        }
      }

      // 初始检查
      setTimeout(checkPlaylistEmpty, 1000);

      // 监听表格变化
      const observer = new MutationObserver(checkPlaylistEmpty);
      const table = document.getElementById('videosTable');
      if (table) {
        observer.observe(table, { childList: true, subtree: true });
      }

      // 添加搜索框焦点效果
      const inputBox = document.getElementById('inputBox');
      if (inputBox) {
        inputBox.addEventListener('focus', function() {
          this.parentElement.parentElement.style.transform = 'scale(1.02)';
        });

        inputBox.addEventListener('blur', function() {
          this.parentElement.parentElement.style.transform = 'scale(1)';
        });
      }

      // 添加页面加载动画
      document.body.style.opacity = '0';
      document.body.style.transition = 'opacity 0.5s ease-in-out';

      setTimeout(function() {
        document.body.style.opacity = '1';
      }, 100);

      // 添加滚动效果
      let lastScrollTop = 0;
      const header = document.querySelector('header');

      window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > lastScrollTop && scrollTop > 100) {
          // 向下滚动
          header.style.transform = 'translateY(-100%)';
        } else {
          // 向上滚动
          header.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;
      });
    });

    // 搜索结果显示时隐藏欢迎消息
    function hideWelcomeMessage() {
      const welcomeMessage = document.getElementById('welcomeMessage');
      if (welcomeMessage) {
        welcomeMessage.style.display = 'none';
      }
    }

    // 全局函数，供其他脚本调用
    window.modernUI = {
      hideWelcomeMessage: hideWelcomeMessage
    };

    // 测试模态框显示函数
    function testShowModal() {
      console.log('🔧 测试显示模态框...');

      // 首先隐藏footer
      const footer = document.querySelector('footer');
      if (footer) {
        footer.style.display = 'none';
        footer.style.visibility = 'hidden';
        footer.style.opacity = '0';
        footer.style.zIndex = '-1';
        console.log('✅ 已隐藏footer区域');
      }

      const modal = document.getElementById('quickCreatePlaylistModal');

      if (modal) {
        console.log('✅ 找到模态框元素');
        modal.style.display = 'flex';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.style.zIndex = '2147483647';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100vw';
        modal.style.height = '100vh';
        modal.style.background = 'rgba(0, 0, 0, 0.8)';

        console.log('✅ 模态框样式已设置');

        // 清空输入框
        const nameInput = document.getElementById('quickPlaylistName');
        const descInput = document.getElementById('quickPlaylistDescription');
        if (nameInput) nameInput.value = '';
        if (descInput) descInput.value = '';

        console.log('✅ 输入框已清空');
      } else {
        console.error('❌ 未找到 quickCreatePlaylistModal 元素');
        alert('未找到模态框元素！');
      }
    }

    // 关闭模态框函数
    function closeQuickCreateDialog() {
      console.log('🔧 关闭模态框...');
      const modal = document.getElementById('quickCreatePlaylistModal');
      if (modal) {
        modal.style.display = 'none';

        // 恢复播放区显示
        const footer = document.querySelector('footer');
        if (footer) {
          footer.style.display = 'flex';
          footer.style.visibility = 'visible';
          footer.style.opacity = '1';
          footer.style.minHeight = '300px';
          footer.style.height = 'auto';
          footer.style.zIndex = '1000';

          console.log('✅ 播放区已恢复显示');
        }

        console.log('✅ 模态框已关闭');
      }
    }

    // 创建播放列表函数
    function createQuickPlaylist() {
      console.log('🔧 创建播放列表...');
      const nameInput = document.getElementById('quickPlaylistName');

      if (nameInput && nameInput.value.trim()) {
        alert('播放列表 "' + nameInput.value + '" 创建成功！');
        closeQuickCreateDialog();
      } else {
        alert('请输入播放列表名称！');
      }
    }

    // 更新存储路径信息
    function updateStoragePathInfo() {
      if (window.localPlaylistManager) {
        const storageInfo = window.localPlaylistManager.getLocalStoragePath();
        const pathElement = document.getElementById('storagePath');

        if (pathElement) {
          pathElement.textContent = storageInfo.path || '无法检测路径';
          pathElement.title = `浏览器: ${storageInfo.browser} | 系统: ${storageInfo.os} | 域名: ${storageInfo.domain}`;
        }
      }
    }

    // 切换存储信息显示
    function toggleStorageInfo() {
      console.log('🔧 切换存储信息显示...');

      const content = document.getElementById('storageInfoContent');
      const toggleBtn = document.querySelector('.info-toggle-btn');

      console.log('📋 找到元素:', {
        content: !!content,
        toggleBtn: !!toggleBtn,
        currentDisplay: content ? content.style.display : 'null'
      });

      if (content && toggleBtn) {
        const isHidden = content.style.display === 'none' || content.style.display === '';

        if (isHidden) {
          console.log('📖 显示存储信息内容');
          content.style.display = 'block';
          content.style.visibility = 'visible';
          content.style.opacity = '1';
          content.style.maxHeight = '1000px';
          toggleBtn.classList.add('expanded');

          // 检查内容是否完整显示并确保最后一项可见
          setTimeout(() => {
            const allItems = content.querySelectorAll('li');
            console.log('📋 存储信息项目数量:', allItems.length);

            const lastItem = allItems[allItems.length - 1];
            const modalBody = content.closest('.modal-body');

            if (lastItem && modalBody) {
              // 确保有足够的底部间距
              const storageSection = content.closest('.storage-info-section');
              if (storageSection) {
                storageSection.style.paddingBottom = '20px';
              }

              // 检查最后一项是否完全可见
              setTimeout(() => {
                const rect = lastItem.getBoundingClientRect();
                const modalRect = modalBody.getBoundingClientRect();
                const isFullyVisible = rect.bottom <= (modalRect.bottom - 10); // 留10px缓冲

                console.log('📋 最后一项信息:', {
                  text: lastItem.textContent,
                  fullyVisible: isFullyVisible,
                  itemBottom: rect.bottom,
                  modalBottom: modalRect.bottom,
                  buffer: modalRect.bottom - rect.bottom
                });

                // 如果最后一项不完全可见，调整滚动位置
                if (!isFullyVisible) {
                  const scrollOffset = rect.bottom - modalRect.bottom + 20; // 额外20px缓冲
                  modalBody.scrollTop += scrollOffset;
                  console.log('📜 调整滚动位置:', scrollOffset);
                }
              }, 200);
            }
          }, 100);
        } else {
          console.log('📖 隐藏存储信息内容');
          content.style.display = 'none';
          content.style.visibility = 'hidden';
          content.style.opacity = '0';
          toggleBtn.classList.remove('expanded');
        }

        console.log('✅ 切换完成，当前状态:', content.style.display);
      } else {
        console.error('❌ 未找到必要的元素');
        if (!content) console.error('❌ 未找到 storageInfoContent 元素');
        if (!toggleBtn) console.error('❌ 未找到 info-toggle-btn 元素');
      }
    }

    // 复制存储路径
    function copyStoragePath() {
      const pathElement = document.getElementById('storagePath');
      if (pathElement) {
        const path = pathElement.textContent;

        if (navigator.clipboard) {
          navigator.clipboard.writeText(path).then(() => {
            showCopyFeedback('路径已复制到剪贴板');
          }).catch(() => {
            fallbackCopyText(path);
          });
        } else {
          fallbackCopyText(path);
        }
      }
    }

    // 备用复制方法
    function fallbackCopyText(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();

      try {
        document.execCommand('copy');
        showCopyFeedback('路径已复制到剪贴板');
      } catch (err) {
        showCopyFeedback('复制失败，请手动复制', 'error');
      }

      document.body.removeChild(textArea);
    }

    // 显示复制反馈
    function showCopyFeedback(message, type = 'success') {
      const feedback = document.createElement('div');
      feedback.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: ${type === 'success' ? '#4caf50' : '#f44336'};
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        z-index: 100001;
        font-size: 12px;
        animation: fadeInOut 2s ease;
      `;
      feedback.textContent = message;

      document.body.appendChild(feedback);

      setTimeout(() => {
        if (document.body.contains(feedback)) {
          document.body.removeChild(feedback);
        }
      }, 2000);
    }

    // 显示备份说明
    function showBackupInstructions() {
      const instructions = `
📋 播放列表备份方法：

方法一：通过开发者工具
1. 按 F12 打开开发者工具
2. 转到 Application 标签页
3. 在左侧找到 Local Storage
4. 展开网站域名
5. 找到 'streamly-local-playlists' 键
6. 复制其值并保存到文件

方法二：通过控制台
1. 按 F12 打开开发者工具
2. 转到 Console 标签页
3. 输入：localStorage.getItem('streamly-local-playlists')
4. 复制输出结果并保存

恢复方法：
将备份的数据通过控制台输入：
localStorage.setItem('streamly-local-playlists', '备份数据')
然后刷新页面即可。
      `;

      alert(instructions);
    }

    // 滚动到存储信息底部的函数
    function scrollToStorageBottom() {
      const content = document.getElementById('storageInfoContent');
      const modalBody = content ? content.closest('.modal-body') : null;

      if (modalBody) {
        // 滚动到底部
        modalBody.scrollTop = modalBody.scrollHeight;
        console.log('📜 已滚动到存储信息底部');
      }
    }

    // 确保弹窗底部内容可见
    function ensureModalBottomVisible() {
      const modal = document.getElementById('quickCreatePlaylistModal');
      const modalContent = modal ? modal.querySelector('.modal-content') : null;
      const modalFooter = modalContent ? modalContent.querySelector('.modal-footer') : null;

      if (modal && modalContent && modalFooter) {
        // 检查弹窗是否超出屏幕高度
        const modalRect = modalContent.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        if (modalRect.bottom > viewportHeight - 20) {
          // 如果弹窗底部超出屏幕，调整位置
          modal.style.alignItems = 'flex-start';
          modal.style.paddingTop = '20px';
          modal.style.paddingBottom = '20px';

          console.log('✅ 已调整弹窗位置以确保底部可见');
        }
      }
    }

    // 全局调试函数
    window.debugStorageInfo = function() {
      const content = document.getElementById('storageInfoContent');
      const modalBody = content ? content.closest('.modal-body') : null;

      if (content && modalBody) {
        const allItems = content.querySelectorAll('li');
        const lastItem = allItems[allItems.length - 1];

        console.log('=== 存储信息调试 ===');
        console.log('总项目数:', allItems.length);
        console.log('内容高度:', content.scrollHeight);
        console.log('模态框主体高度:', modalBody.clientHeight);
        console.log('滚动位置:', modalBody.scrollTop);
        console.log('最大滚动:', modalBody.scrollHeight - modalBody.clientHeight);

        if (lastItem) {
          console.log('最后一项文本:', lastItem.textContent);
          const rect = lastItem.getBoundingClientRect();
          const modalRect = modalBody.getBoundingClientRect();
          console.log('最后一项可见:', rect.bottom <= modalRect.bottom);
        }

        return {
          totalItems: allItems.length,
          contentHeight: content.scrollHeight,
          modalHeight: modalBody.clientHeight,
          scrollTop: modalBody.scrollTop,
          maxScroll: modalBody.scrollHeight - modalBody.clientHeight,
          lastItemText: lastItem ? lastItem.textContent : 'none'
        };
      }

      return null;
    };

    // 强制显示所有内容的函数
    window.forceShowAllStorageInfo = function() {
      const modal = document.getElementById('quickCreatePlaylistModal');
      const modalContent = modal ? modal.querySelector('.modal-content') : null;
      const modalBody = modalContent ? modalContent.querySelector('.modal-body') : null;

      if (modalBody) {
        modalBody.style.maxHeight = '80vh';
        modalBody.style.overflowY = 'auto';
        console.log('✅ 已强制设置模态框主体高度');

        // 展开存储信息
        const content = document.getElementById('storageInfoContent');
        if (content && content.style.display === 'none') {
          toggleStorageInfo();
        }

        // 滚动到底部
        setTimeout(() => {
          scrollToStorageBottom();
        }, 300);
      }
    };

    // 全局恢复播放区函数
    window.restoreFooter = function() {
      console.log('🔧 全局恢复播放区...');
      const footer = document.querySelector('footer');

      if (footer) {
        footer.style.display = 'flex';
        footer.style.visibility = 'visible';
        footer.style.opacity = '1';
        footer.style.position = 'relative';
        footer.style.zIndex = '1000';
        footer.style.minHeight = '300px';
        footer.style.height = 'auto';

        console.log('✅ 播放区已全局恢复');
        return true;
      } else {
        console.error('❌ 未找到footer元素');
        return false;
      }
    };

    // 检查播放区状态的函数
    window.checkFooterStatus = function() {
      const footer = document.querySelector('footer');

      if (footer) {
        const computedStyle = window.getComputedStyle(footer);
        const status = {
          exists: true,
          display: footer.style.display || computedStyle.display,
          visibility: footer.style.visibility || computedStyle.visibility,
          opacity: footer.style.opacity || computedStyle.opacity,
          zIndex: footer.style.zIndex || computedStyle.zIndex
        };

        console.log('📊 播放区状态:', status);
        return status;
      } else {
        console.error('❌ 未找到footer元素');
        return { exists: false };
      }
    };

    // 强制修复播放区高度的函数
    window.fixFooterHeight = function() {
      console.log('🔧 强制修复播放区高度...');
      const footer = document.querySelector('footer');

      if (footer) {
        // 移除所有可能的高度限制
        footer.style.height = '';
        footer.style.maxHeight = '';
        footer.style.minHeight = '300px';

        // 确保正确的显示属性
        footer.style.display = 'flex';
        footer.style.visibility = 'visible';
        footer.style.opacity = '1';
        footer.style.position = 'relative';
        footer.style.zIndex = '1000';

        // 强制重新计算布局
        footer.offsetHeight;

        console.log('✅ 播放区高度已强制修复');
        console.log('📏 当前高度:', footer.offsetHeight + 'px');
        return footer.offsetHeight;
      } else {
        console.error('❌ 未找到footer元素');
        return 0;
      }
    };

    // 页面加载完成后自动修复footer高度
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(() => {
        fixFooterHeight();
      }, 1000);

      // 初始化搜索按钮功能
      initializeSearchButton();
    });

    // 初始化搜索按钮功能
    function initializeSearchButton() {
      const searchBtn = document.getElementById('searchButton');
      const inputBox = document.getElementById('inputBox');

      if (searchBtn && inputBox) {
        // 点击搜索按钮时执行搜索
        searchBtn.addEventListener('click', function(e) {
          e.preventDefault();
          performSearch();
        });

        // 回车键搜索
        inputBox.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            performSearch();
          }
        });

        // 输入框获得焦点时搜索按钮高亮
        inputBox.addEventListener('focus', function() {
          searchBtn.style.background = 'linear-gradient(135deg, #29b6f6, #0288d1)';
          searchBtn.style.boxShadow = '0 4px 15px rgba(79, 195, 247, 0.5)';
        });

        // 输入框失去焦点时搜索按钮恢复
        inputBox.addEventListener('blur', function() {
          searchBtn.style.background = 'linear-gradient(135deg, #4fc3f7, #29b6f6)';
          searchBtn.style.boxShadow = '0 2px 8px rgba(79, 195, 247, 0.3)';
        });

        console.log('✅ 搜索按钮功能已初始化');
      }
    }

    // 执行搜索的函数
    function performSearch() {
      const inputBox = document.getElementById('inputBox');
      const searchBtn = document.getElementById('searchButton');

      if (inputBox && inputBox.value.trim()) {
        console.log('🔍 开始搜索:', inputBox.value.trim());

        // 添加搜索按钮的加载动画
        if (searchBtn) {
          searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
          searchBtn.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
        }

        // 调用原有的搜索函数
        if (typeof input === 'function') {
          input(0);
        }

        // 恢复搜索按钮
        setTimeout(() => {
          if (searchBtn) {
            searchBtn.innerHTML = '<i class="fas fa-search"></i>';
            searchBtn.style.background = 'linear-gradient(135deg, #4fc3f7, #29b6f6)';
          }
        }, 1000);
      } else {
        // 输入框为空时的提示
        if (inputBox) {
          inputBox.focus();
          inputBox.style.borderColor = '#ff5722';
          inputBox.style.boxShadow = '0 0 10px rgba(255, 87, 34, 0.3)';

          setTimeout(() => {
            inputBox.style.borderColor = 'rgba(79, 195, 247, 0.3)';
            inputBox.style.boxShadow = '';
          }, 2000);
        }

        console.log('⚠️ 请输入搜索关键词');
      }
    }

    // 强制确保模态框在最顶层的函数
    window.forceModalToTop = function() {
      const modal = document.getElementById('quickCreatePlaylistModal');
      if (modal) {
        // 设置绝对最高的z-index
        modal.style.zIndex = '2147483647';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.right = '0';
        modal.style.bottom = '0';
        modal.style.width = '100vw';
        modal.style.height = '100vh';

        // 设置所有子元素的z-index
        const allElements = modal.querySelectorAll('*');
        allElements.forEach(element => {
          element.style.zIndex = '2147483647';
          element.style.position = 'relative';
        });

        console.log('✅ 模态框已强制置顶');
        return true;
      }
      return false;
    };

    // 监听模态框显示，自动设置最高层级
    function setupModalObserver() {
      const modal = document.getElementById('quickCreatePlaylistModal');
      if (modal) {
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
              if (modal.style.display === 'flex' || modal.style.display === 'block') {
                setTimeout(() => {
                  forceModalToTop();
                }, 10);
              }
            }
          });
        });

        observer.observe(modal, {
          attributes: true,
          attributeFilter: ['style']
        });

        console.log('✅ 模态框观察器已设置');
      }
    }

    // 页面加载完成后设置观察器
    document.addEventListener('DOMContentLoaded', function() {
      setupModalObserver();

      // 立即检查并强制设置模态框层级
      setTimeout(() => {
        forceModalToTop();
      }, 100);
    });

    // 重写showCreatePlaylistDialog函数以确保层级
    const originalShowCreatePlaylistDialog = window.showCreatePlaylistDialog;
    window.showCreatePlaylistDialog = function() {
      if (originalShowCreatePlaylistDialog) {
        originalShowCreatePlaylistDialog.apply(this, arguments);
      }

      // 强制设置最高层级
      setTimeout(() => {
        forceModalToTop();
      }, 50);
    };

    // 初始化窗口管理器
    document.addEventListener('DOMContentLoaded', function() {
      if (typeof StableWindowManager !== 'undefined') {
        window.stableWindowManager = new StableWindowManager();
        console.log('✅ 窗口管理器已初始化');
      } else {
        console.error('❌ StableWindowManager类未找到');
      }
    });
  </script>
</body>
</html>
