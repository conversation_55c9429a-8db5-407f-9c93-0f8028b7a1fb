<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗居中测试 - Streamly</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 模拟Streamly的Header */
        header {
            background: rgba(15, 15, 35, 0.9);
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #4fc3f7;
        }

        /* 模拟Streamly的Footer */
        footer {
            background: rgba(15, 15, 35, 0.9);
            height: 400px;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-top: 1px solid rgba(79, 195, 247, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-content {
            margin-top: 80px;
            margin-bottom: 420px;
            padding: 20px;
            text-align: center;
            min-height: calc(100vh - 500px);
        }

        .status-panel {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 300px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-size: 11px;
            z-index: 3000;
            font-family: monospace;
        }

        .test-btn {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            padding: 12px 20px;
            margin: 10px;
            font-size: 14px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            margin-top: 10px;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 5px;
            font-size: 10px;
        }

        .center-guide {
            position: fixed;
            border: 2px dashed rgba(255, 255, 0, 0.5);
            pointer-events: none;
            z-index: 2000;
        }

        .center-guide.horizontal {
            left: 0;
            right: 0;
            height: 2px;
        }

        .center-guide.vertical {
            top: 0;
            bottom: 0;
            width: 2px;
        }
    </style>
    
    <!-- 引入Streamly的CSS -->
    <link rel="stylesheet" href="res/css/styles.css">
    <link rel="stylesheet" href="res/css/modern-theme.css">
</head>
<body>
    <header>
        <div class="logo">📐 弹窗居中测试 - Header (70px)</div>
    </header>

    <div class="test-content">
        <h3>📱 测试搜索结果弹窗的居中逻辑</h3>
        <p>验证弹窗是否在Header和Footer之间正确居中</p>
        
        <button class="test-btn" onclick="showSearchWindow()">显示搜索结果弹窗</button>
        <button class="test-btn" onclick="hideSearchWindow()">隐藏弹窗</button>
        <button class="test-btn" onclick="toggleCenterGuides()">切换居中参考线</button>
        <button class="test-btn" onclick="recenterWindow()">重新居中</button>
        
        <div style="margin-top: 30px; font-size: 12px;">
            <h4>🎯 居中验证：</h4>
            <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                <li>✅ Header高度: 70px (固定在顶部)</li>
                <li>✅ Footer高度: 400px (固定在底部)</li>
                <li>✅ 可用区域: window.innerHeight - 470px</li>
                <li>✅ 弹窗应该在可用区域的正中央</li>
            </ul>
        </div>
    </div>

    <footer>
        <div style="text-align: center;">
            <div style="font-size: 16px; font-weight: bold; color: #4fc3f7;">Footer (400px)</div>
            <div style="font-size: 12px; margin-top: 10px;">模拟Streamly的播放列表区域</div>
        </div>
    </footer>

    <!-- 居中参考线 -->
    <div id="horizontalGuide" class="center-guide horizontal" style="display: none;"></div>
    <div id="verticalGuide" class="center-guide vertical" style="display: none;"></div>

    <!-- 使用Streamly原始的搜索结果窗口结构 -->
    <div id="searchResultsWindow" class="floatingMenu" style="display: none;">
        <h2>搜索结果:</h2>
        <div class="floatingMenuCloseButton" onclick="hideSearchWindow()">关闭</div>
        <div id="searchStatus" style="padding: 10px; font-style: italic; color: #666; display: none;"></div>
        <div id="searchResults" class="floatingMenuContents">
            <div style="padding: 20px;">
                <p><strong>🎯 居中测试弹窗</strong></p>
                <p>这个弹窗应该在Header和Footer之间的中央位置</p>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                    <strong>预期位置：</strong><br>
                    • 水平居中：屏幕宽度的中央<br>
                    • 垂直居中：Header下边界和Footer上边界之间的中央<br>
                    • 不应该被Header或Footer遮挡
                </div>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(79, 195, 247, 0.1); border-radius: 8px;">
                    <strong>当前位置：</strong><br>
                    <span id="currentPosition">计算中...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态面板 -->
    <div class="status-panel">
        <div><strong>📊 居中测试状态</strong></div>
        <div class="info-grid">
            <div class="info-item">
                <strong>屏幕尺寸:</strong><br>
                <span id="screenSize">-</span>
            </div>
            <div class="info-item">
                <strong>Header高度:</strong><br>
                <span id="headerHeight">-</span>
            </div>
            <div class="info-item">
                <strong>Footer高度:</strong><br>
                <span id="footerHeight">-</span>
            </div>
            <div class="info-item">
                <strong>可用区域:</strong><br>
                <span id="availableArea">-</span>
            </div>
            <div class="info-item">
                <strong>弹窗尺寸:</strong><br>
                <span id="windowSize">-</span>
            </div>
            <div class="info-item">
                <strong>理论中心:</strong><br>
                <span id="theoreticalCenter">-</span>
            </div>
            <div class="info-item">
                <strong>实际位置:</strong><br>
                <span id="actualPosition">-</span>
            </div>
            <div class="info-item">
                <strong>居中状态:</strong><br>
                <span id="centeringStatus">-</span>
            </div>
        </div>
    </div>

    <!-- 引入jQuery（Streamly依赖） -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    
    <!-- 引入窗口管理器 -->
    <script src="res/js/stable-window-manager.js"></script>

    <script>
        let searchWindow = document.getElementById('searchResultsWindow');
        let guidesVisible = false;
        
        function updateInfo() {
            const header = document.querySelector('header');
            const footer = document.querySelector('footer');
            const headerHeight = header ? header.offsetHeight : 0;
            const footerHeight = footer ? footer.offsetHeight : 0;
            const availableHeight = window.innerHeight - headerHeight - footerHeight;
            const availableWidth = window.innerWidth;
            
            document.getElementById('screenSize').textContent = `${window.innerWidth} x ${window.innerHeight}`;
            document.getElementById('headerHeight').textContent = `${headerHeight}px`;
            document.getElementById('footerHeight').textContent = `${footerHeight}px`;
            document.getElementById('availableArea').textContent = `${availableWidth} x ${availableHeight}`;
            
            if (searchWindow.style.display !== 'none') {
                const rect = searchWindow.getBoundingClientRect();
                const windowWidth = rect.width;
                const windowHeight = rect.height;
                
                document.getElementById('windowSize').textContent = `${Math.round(windowWidth)} x ${Math.round(windowHeight)}`;
                
                // 计算理论中心位置
                const theoreticalCenterX = (availableWidth - windowWidth) / 2;
                const theoreticalCenterY = headerHeight + (availableHeight - windowHeight) / 2;
                
                document.getElementById('theoreticalCenter').textContent = `(${Math.round(theoreticalCenterX)}, ${Math.round(theoreticalCenterY)})`;
                document.getElementById('actualPosition').textContent = `(${Math.round(rect.left)}, ${Math.round(rect.top)})`;
                document.getElementById('currentPosition').textContent = `left: ${Math.round(rect.left)}px, top: ${Math.round(rect.top)}px`;
                
                // 检查居中状态
                const deltaX = Math.abs(rect.left - theoreticalCenterX);
                const deltaY = Math.abs(rect.top - theoreticalCenterY);
                
                if (deltaX <= 5 && deltaY <= 5) {
                    document.getElementById('centeringStatus').textContent = '✅ 完美居中';
                    document.getElementById('centeringStatus').style.color = '#4caf50';
                } else if (deltaX <= 20 && deltaY <= 20) {
                    document.getElementById('centeringStatus').textContent = `⚠️ 基本居中 (偏差: ${Math.round(deltaX)}, ${Math.round(deltaY)})`;
                    document.getElementById('centeringStatus').style.color = '#ff9800';
                } else {
                    document.getElementById('centeringStatus').textContent = `❌ 偏离中心 (偏差: ${Math.round(deltaX)}, ${Math.round(deltaY)})`;
                    document.getElementById('centeringStatus').style.color = '#f44336';
                }
                
                // 更新参考线位置
                if (guidesVisible) {
                    document.getElementById('horizontalGuide').style.top = theoreticalCenterY + windowHeight/2 + 'px';
                    document.getElementById('verticalGuide').style.left = theoreticalCenterX + windowWidth/2 + 'px';
                }
            } else {
                document.getElementById('windowSize').textContent = '-';
                document.getElementById('theoreticalCenter').textContent = '-';
                document.getElementById('actualPosition').textContent = '-';
                document.getElementById('centeringStatus').textContent = '未显示';
                document.getElementById('currentPosition').textContent = '弹窗未显示';
            }
        }

        function showSearchWindow() {
            searchWindow.style.display = 'block';
            
            // 等待窗口管理器增强
            setTimeout(() => {
                if (window.stableWindowManager) {
                    console.log('🎯 使用窗口管理器居中');
                    window.stableWindowManager.centerWindow(searchWindow);
                } else {
                    console.log('🎯 使用手动居中');
                    manualCenter();
                }
                updateInfo();
            }, 100);
        }

        function hideSearchWindow() {
            searchWindow.style.display = 'none';
            updateInfo();
        }

        function manualCenter() {
            const header = document.querySelector('header');
            const footer = document.querySelector('footer');
            const headerHeight = header ? header.offsetHeight : 70;
            const footerHeight = footer ? footer.offsetHeight : 400;
            
            const availableHeight = window.innerHeight - headerHeight - footerHeight;
            const rect = searchWindow.getBoundingClientRect();
            
            const centerX = (window.innerWidth - rect.width) / 2;
            const centerY = headerHeight + (availableHeight - rect.height) / 2;
            
            searchWindow.style.left = Math.max(0, centerX) + 'px';
            searchWindow.style.top = Math.max(headerHeight, centerY) + 'px';
        }

        function recenterWindow() {
            if (searchWindow.style.display !== 'none') {
                if (window.stableWindowManager) {
                    window.stableWindowManager.centerWindow(searchWindow);
                } else {
                    manualCenter();
                }
                updateInfo();
            }
        }

        function toggleCenterGuides() {
            guidesVisible = !guidesVisible;
            document.getElementById('horizontalGuide').style.display = guidesVisible ? 'block' : 'none';
            document.getElementById('verticalGuide').style.display = guidesVisible ? 'block' : 'none';
            updateInfo();
        }

        // 监听窗口大小变化
        window.addEventListener('resize', updateInfo);
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                updateInfo();
                
                // 自动创建窗口管理器实例
                if (typeof StableWindowManager !== 'undefined' && !window.stableWindowManager) {
                    window.stableWindowManager = new StableWindowManager();
                    console.log('✅ 窗口管理器已创建');
                }
            }, 500);
        });

        // 定期更新信息
        setInterval(updateInfo, 1000);
    </script>
</body>
</html>
