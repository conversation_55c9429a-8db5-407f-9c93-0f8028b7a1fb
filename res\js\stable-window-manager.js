/**
 * Streamly 稳定窗口管理器
 * 支持电脑端鼠标拖拽和手机端触摸拖拽，包含边界限制功能
 */

class StableWindowManager {
    constructor() {
        this.activeWindow = null;
        this.isDragging = false;
        this.isResizing = false;
        this.dragOffset = { x: 0, y: 0 };
        this.resizeHandle = null;
        this.minWidth = 300;
        this.minHeight = 200;
        this.headerHeight = 0;
        this.touchStartInfo = null;

        // 窗口选择器
        this.windowSelectors = [
            '.floatingMenu',
            '.modal',
            '#searchResultsWindow',
            '#settingsWindow',
            '#playlistManagerModal',
            '#createPlaylistModal',
            '.window-draggable'
        ];

        this.init();
    }
    
    init() {
        this.setupGlobalEvents();
        this.setupWindows();
    }
    
    setupGlobalEvents() {
        // 鼠标事件（电脑端）
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.addEventListener('mouseup', () => this.handleMouseUp());

        // 触摸事件（手机端）
        document.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        document.addEventListener('touchend', () => this.handleTouchEnd());
        document.addEventListener('touchcancel', () => this.handleTouchEnd());

        // 防止拖拽时选择文本
        document.addEventListener('selectstart', (e) => {
            if (this.isDragging || this.isResizing) {
                e.preventDefault();
            }
        });
    }
    
    setupWindows() {
        // 为现有窗口添加功能
        this.enhanceExistingWindows();
        
        // 监听新窗口
        this.observeNewWindows();
    }
    
    enhanceExistingWindows() {
        this.windowSelectors.forEach(selector => {
            const windows = document.querySelectorAll(selector);
            windows.forEach(window => this.enhanceWindow(window));
        });
    }
    
    isWindowElement(element) {
        return this.windowSelectors.some(selector => element.matches(selector));
    }
    
    enhanceWindow(windowElement) {
        if (windowElement.dataset.enhanced === 'true') {
            return; // 已经增强过了
        }
        
        console.log('🔧 增强窗口:', windowElement);
        
        // 标记为已增强
        windowElement.dataset.enhanced = 'true';
        
        // 设置基本样式
        this.setupBasicStyles(windowElement);
        
        // 添加拖拽功能
        this.addDragFunctionality(windowElement);
        
        // 添加关闭按钮功能
        this.enhanceCloseButton(windowElement);
    }
    
    setupBasicStyles(windowElement) {
        // 设置基本样式
        if (!windowElement.style.position || windowElement.style.position === 'static') {
            windowElement.style.position = 'fixed';
        }

        if (!windowElement.style.zIndex) {
            windowElement.style.zIndex = '1000';
        }

        // 检查是否需要居中
        const hasValidPosition = windowElement.style.left && windowElement.style.top;
        const rect = windowElement.getBoundingClientRect();
        const isInReasonablePosition = rect.left > 10 && rect.top > 10;

        if (!hasValidPosition && !isInReasonablePosition) {
            this.centerWindow(windowElement);
        }
    }
    
    centerWindow(windowElement) {
        const rect = windowElement.getBoundingClientRect();
        const headerHeight = this.getHeaderHeight();
        const footerHeight = this.getFooterHeight();

        // 计算Header和Footer之间的可用区域
        const availableHeight = window.innerHeight - headerHeight - footerHeight;
        const availableWidth = window.innerWidth;

        // 在可用区域中居中
        const centerX = (availableWidth - rect.width) / 2;
        const centerY = headerHeight + (availableHeight - rect.height) / 2;

        // 确保窗口不会超出边界
        const constrainedX = Math.max(0, Math.min(centerX, availableWidth - rect.width));
        const constrainedY = Math.max(headerHeight, Math.min(centerY, headerHeight + availableHeight - rect.height));

        windowElement.style.left = constrainedX + 'px';
        windowElement.style.top = constrainedY + 'px';
    }
    
    addDragFunctionality(windowElement) {
        // 查找拖拽区域（标题栏）
        const dragArea = windowElement.querySelector('h2, .modal-header, .window-drag-area') || windowElement;
        
        if (!dragArea) return;
        
        // 设置拖拽区域样式
        dragArea.style.cursor = 'move';
        dragArea.style.userSelect = 'none';
        
        // 鼠标事件（电脑端）
        dragArea.addEventListener('mousedown', (e) => this.startDrag(e, windowElement));

        // 触摸事件（手机端）
        dragArea.addEventListener('touchstart', (e) => this.startTouchDrag(e, windowElement), { passive: false });
    }
    
    enhanceCloseButton(windowElement) {
        const closeBtn = windowElement.querySelector('.floatingMenuCloseButton, .modal-close, .close');
        if (closeBtn) {
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.userSelect = 'none';
        }
    }
    
    // 电脑端拖拽开始
    startDrag(e, windowElement) {
        // 避免在控制按钮上开始拖拽
        if (e.target.closest('.floatingMenuCloseButton, .modal-close, .window-controls')) {
            return;
        }

        this.isDragging = true;
        this.activeWindow = windowElement;

        const rect = windowElement.getBoundingClientRect();
        this.dragOffset.x = e.clientX - rect.left;
        this.dragOffset.y = e.clientY - rect.top;

        // 获取Header高度，用于后续边界检查
        this.headerHeight = this.getHeaderHeight();

        // 添加拖拽状态样式
        windowElement.style.zIndex = '10001';
        windowElement.classList.add('being-dragged');
        document.body.style.userSelect = 'none';
        document.body.style.cursor = 'move';
        document.body.classList.add('window-dragging');

        e.preventDefault();
    }
    
    // 手机端拖拽开始
    startTouchDrag(e, windowElement) {
        // 避免在控制按钮上开始拖拽
        if (e.target.closest('.floatingMenuCloseButton, .modal-close, .window-controls')) {
            return;
        }

        const touch = e.touches[0];
        if (!touch) return;

        // 获取当前CSS位置值作为基准
        const computedStyle = window.getComputedStyle(windowElement);
        let cssLeft = parseFloat(windowElement.style.left) || 0;
        let cssTop = parseFloat(windowElement.style.top) || 0;

        if (cssLeft === 0 && windowElement.style.left === '') {
            cssLeft = parseFloat(computedStyle.left) || 0;
        }
        if (cssTop === 0 && windowElement.style.top === '') {
            cssTop = parseFloat(computedStyle.top) || 0;
        }

        // 记录触摸信息
        this.touchStartInfo = {
            startX: touch.clientX,
            startY: touch.clientY,
            windowElement: windowElement,
            initialWindowTop: cssTop,
            initialWindowLeft: cssLeft,
            hasMoved: false,
            isDragging: false
        };

        e.preventDefault();
    }
    
    getHeaderHeight() {
        const header = document.querySelector('header');
        if (header && header.style.display !== 'none') {
            const height = header.offsetHeight;
            return height;
        }
        return 70; // Streamly的标准Header高度
    }

    getFooterHeight() {
        return 400; // Streamly的标准Footer高度
    }

    // 电脑端鼠标移动处理
    handleMouseMove(e) {
        if (!this.isDragging || !this.activeWindow) return;

        // 计算期望的新位置
        const newX = e.clientX - this.dragOffset.x;
        const newY = e.clientY - this.dragOffset.y;

        // 获取Header和Footer高度
        const headerHeight = this.headerHeight || this.getHeaderHeight();
        const footerHeight = this.getFooterHeight();

        // 获取弹窗尺寸
        const windowWidth = this.activeWindow.offsetWidth;
        const minVisibleWidth = 150; // 最小可见宽度
        const titleBarHeight = 50;

        // 🔧 基于弹窗右边界的一致性边界限制
        // 计算弹窗的右边界位置
        const windowRightEdge = newX + windowWidth;

        // 定义弹窗右边界的限制范围
        const minRightEdge = minVisibleWidth; // 弹窗右边界最左位置：至少150px可见
        const maxRightEdge = window.innerWidth; // 弹窗右边界最右位置：不超出屏幕

        // 限制弹窗右边界
        const constrainedRightEdge = Math.max(minRightEdge, Math.min(windowRightEdge, maxRightEdge));

        // 根据限制后的右边界计算左边界
        const constrainedX = constrainedRightEdge - windowWidth;

        // 垂直边界：弹窗顶部不能超过Header下边界
        const absoluteMinY = headerHeight;
        const absoluteMaxY = window.innerHeight - footerHeight - titleBarHeight;
        const constrainedY = Math.max(absoluteMinY, Math.min(newY, absoluteMaxY));

        // 🔧 调试信息：显示边界限制详情
        if (windowRightEdge !== constrainedRightEdge) {
            console.log(`🖱️ 基于右边界的限制:`);
            console.log(`   期望右边界: ${windowRightEdge}px`);
            console.log(`   限制后右边界: ${constrainedRightEdge}px`);
            console.log(`   最终左边界: ${constrainedX}px`);
            console.log(`   右边界范围: [${minRightEdge}, ${maxRightEdge}]`);
            console.log(`   dragOffset.x=${this.dragOffset.x}px`);
        }

        this.activeWindow.style.left = constrainedX + 'px';
        this.activeWindow.style.top = constrainedY + 'px';
    }

    // 手机端触摸移动处理
    handleTouchMove(e) {
        const touch = e.touches[0];
        if (!touch) return;

        // 如果已经在拖拽中，继续垂直拖拽
        if (this.touchStartInfo && this.touchStartInfo.isDragging) {
            this.handleStableVerticalDrag(touch);
            e.preventDefault();
            e.stopPropagation();
            return;
        }

        // 检查是否应该激活拖拽
        if (this.touchStartInfo && !this.touchStartInfo.hasMoved) {
            const deltaY = touch.clientY - this.touchStartInfo.startY;

            // 只有当垂直移动距离超过阈值时才激活拖拽
            if (Math.abs(deltaY) > 15) {
                console.log('📱 激活垂直拖拽');
                this.touchStartInfo.hasMoved = true;

                // 激活拖拽状态
                this.isDragging = true;
                this.activeWindow = this.touchStartInfo.windowElement;

                // 获取Header高度
                this.headerHeight = this.getHeaderHeight();

                // 添加拖拽状态样式
                this.activeWindow.style.zIndex = '10001';
                this.activeWindow.classList.add('being-dragged');
                document.body.style.userSelect = 'none';
                document.body.classList.add('window-dragging');

                // 现在开始处理垂直拖拽
                this.handleStableVerticalDrag(touch);
            }

            e.preventDefault();
            e.stopPropagation();
        }
    }

    // 手机端垂直拖拽处理（核心边界限制逻辑）
    handleStableVerticalDrag(touch) {
        if (!this.touchStartInfo || !this.activeWindow) {
            return;
        }

        // 计算垂直移动距离
        const deltaY = touch.clientY - this.touchStartInfo.startY;

        // 计算新的垂直位置（保持水平位置绝对不变）
        const newTop = this.touchStartInfo.initialWindowTop + deltaY;
        const fixedLeft = this.touchStartInfo.initialWindowLeft;

        // 🔧 手机端专用边界限制：严格按照用户需求调整
        const headerHeight = this.headerHeight || this.getHeaderHeight();
        const footerHeight = this.getFooterHeight();
        const titleBarHeight = 50;
        const extraDownwardSpace = 150;

        // 计算Footer的实际位置
        const footerTop = window.innerHeight - footerHeight;

        // 🔧 简单有效的边界限制：直接阻止弹窗移动到Header上方
        // 1. 向上移动：弹窗顶部不能超过Header下边界
        const minY = headerHeight + 5; // Header下方5px，确保弹窗不被遮挡

        // 2. 向下移动：标题栏可以到Footer上边界，弹窗还能再向下50px
        const maxY = footerTop + extraDownwardSpace - titleBarHeight;

        let constrainedTop = Math.max(minY, Math.min(newTop, maxY));

        // 确保水平位置在合理范围内
        const windowWidth = window.innerWidth;
        const minLeft = -50;
        const maxLeft = windowWidth - 100;
        const safeLeft = Math.max(minLeft, Math.min(fixedLeft, maxLeft));

        // 最终安全检查：确保位置值是有效的数字
        if (isNaN(safeLeft) || isNaN(constrainedTop)) {
            console.error(`📱 位置值无效: left=${safeLeft}, top=${constrainedTop}`);
            return;
        }

        // 🔧 强制清除所有可能干扰的样式属性
        this.activeWindow.style.transform = '';
        this.activeWindow.style.translate = '';
        this.activeWindow.style.webkitTransform = '';

        // 🔧 强制设置定位方式
        this.activeWindow.style.position = 'fixed';

        // 🔧 强制设置left/top，使用!important确保优先级
        this.activeWindow.style.setProperty('left', safeLeft + 'px', 'important');
        this.activeWindow.style.setProperty('top', constrainedTop + 'px', 'important');
    }

    // 电脑端鼠标松开
    handleMouseUp() {
        if (this.isDragging || this.isResizing) {
            this.cleanupDragState();
        }
    }

    // 手机端触摸结束
    handleTouchEnd() {
        if (this.isDragging || this.isResizing) {
            this.cleanupDragState();
        }

        // 清理触摸起始信息
        if (this.touchStartInfo) {
            this.touchStartInfo = null;
        }
    }

    // 清理拖拽状态
    cleanupDragState() {
        // 清理拖拽状态样式
        if (this.activeWindow) {
            this.activeWindow.classList.remove('being-dragged');

            // 恢复标题栏背景
            const dragArea = this.activeWindow.querySelector('h2, .modal-header, .window-drag-area');
            if (dragArea) {
                dragArea.style.background = 'rgba(79, 195, 247, 0.1)';
            }
        }

        // 重置所有状态
        this.isDragging = false;
        this.isResizing = false;
        this.activeWindow = null;
        this.resizeHandle = null;

        // 清理全局样式
        document.body.style.userSelect = '';
        document.body.style.cursor = '';
        document.body.classList.remove('window-dragging');
    }

    observeNewWindows() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // 检查新添加的节点
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 检查是否是弹窗元素
                        if (this.isWindowElement(node)) {
                            console.log('🔧 检测到新弹窗，添加功能');
                            this.enhanceWindow(node);
                        }

                        // 检查子元素中是否有弹窗
                        const windows = node.querySelectorAll && node.querySelectorAll(this.windowSelectors.join(', '));
                        if (windows) {
                            windows.forEach(window => {
                                console.log('🔧 检测到新子弹窗，添加功能');
                                this.enhanceWindow(window);
                            });
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// 全局实例
let stableWindowManager;

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    stableWindowManager = new StableWindowManager();
    window.stableWindowManager = stableWindowManager;
    console.log('🔧 稳定窗口管理器已初始化');
});
