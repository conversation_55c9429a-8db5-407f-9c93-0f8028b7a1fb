<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成测试 - Streamly窗口管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: rgba(15, 15, 35, 0.9);
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #4fc3f7;
        }

        .test-content {
            margin-top: 80px;
            padding: 20px;
            text-align: center;
        }

        .status-panel {
            position: fixed;
            bottom: 20px;
            left: 10px;
            right: 10px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            z-index: 3000;
            font-family: monospace;
        }

        .test-btn {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            padding: 12px 20px;
            margin: 10px;
            font-size: 14px;
        }

        .success {
            background: rgba(76, 175, 80, 0.2) !important;
            border: 2px solid #4caf50 !important;
        }

        .error {
            background: rgba(255, 82, 82, 0.2) !important;
            border: 2px solid #ff5252 !important;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 5px;
            font-size: 11px;
        }
    </style>
    
    <!-- 引入Streamly的CSS -->
    <link rel="stylesheet" href="res/css/styles.css">
    <link rel="stylesheet" href="res/css/modern-theme.css">
</head>
<body>
    <div class="header">
        <div class="logo">🔗 Streamly窗口管理器集成测试</div>
    </div>

    <div class="test-content">
        <h3>📱 验证窗口管理器是否正确集成到Streamly</h3>
        <p>测试修复后的正式程序中的拖拽功能</p>
        
        <button class="test-btn" onclick="testWindowManager()">测试窗口管理器</button>
        <button class="test-btn" onclick="showSearchWindow()">显示搜索结果窗口</button>
        <button class="test-btn" onclick="hideSearchWindow()">隐藏窗口</button>
        
        <div style="margin-top: 30px; font-size: 12px;">
            <h4>🔧 集成验证：</h4>
            <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                <li>✅ 检查stable-window-manager.js是否加载</li>
                <li>✅ 验证搜索结果窗口是否被增强</li>
                <li>✅ 测试移动端触摸拖拽功能</li>
                <li>✅ 确认修复是否生效</li>
            </ul>
            
            <div style="margin-top: 20px; padding: 15px; background: rgba(79, 195, 247, 0.1); border-radius: 8px;">
                <strong>测试步骤：</strong><br>
                1. 点击"测试窗口管理器"检查集成状态<br>
                2. 点击"显示搜索结果窗口"<br>
                3. 在移动端用手指拖拽蓝色标题栏<br>
                4. 观察是否还有跳跃问题<br>
                5. 查看下方状态信息
            </div>
        </div>
    </div>

    <!-- 使用Streamly原始的搜索结果窗口结构 -->
    <div id="searchResultsWindow" class="floatingMenu" style="display: none;">
        <h2>搜索结果:</h2>
        <div class="floatingMenuCloseButton" onclick="hideSearchWindow()">关闭</div>
        <div id="searchStatus" style="padding: 10px; font-style: italic; color: #666; display: none;"></div>
        <div id="searchResults" class="floatingMenuContents">
            <div style="padding: 20px;">
                <p><strong>🎯 集成测试窗口</strong></p>
                <p>这是使用Streamly原始结构的搜索结果窗口</p>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                    <strong>预期行为：</strong><br>
                    • 窗口管理器应该自动增强此窗口<br>
                    • 触摸标题栏时不应跳跃<br>
                    • 拖拽应该平滑跟随手指<br>
                    • 边界限制应该正常工作
                </div>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(79, 195, 247, 0.1); border-radius: 8px;">
                    <strong>窗口增强状态：</strong><br>
                    <span id="enhancementStatus">检查中...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态面板 -->
    <div class="status-panel">
        <div><strong>📊 集成测试状态</strong></div>
        <div class="info-grid">
            <div class="info-item">
                <strong>窗口管理器:</strong><br>
                <span id="managerStatus">检查中...</span>
            </div>
            <div class="info-item">
                <strong>窗口增强:</strong><br>
                <span id="windowEnhanced">未检测</span>
            </div>
            <div class="info-item">
                <strong>触摸跳跃:</strong><br>
                <span id="touchJump">未测试</span>
            </div>
            <div class="info-item">
                <strong>拖拽功能:</strong><br>
                <span id="dragFunction">未测试</span>
            </div>
        </div>
        <div style="margin-top: 10px; max-height: 80px; overflow-y: auto; font-size: 10px;">
            <div id="logOutput">等待测试...</div>
        </div>
    </div>

    <!-- 引入jQuery（Streamly依赖） -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    
    <!-- 引入窗口管理器 -->
    <script src="res/js/stable-window-manager.js"></script>

    <script>
        let searchWindow = document.getElementById('searchResultsWindow');
        let touchStartPos = null;
        let logs = [];
        
        function testWindowManager() {
            // 检查窗口管理器是否加载
            if (typeof StableWindowManager !== 'undefined') {
                updateStatus('managerStatus', '✅ 已加载');
                addLog('✅ StableWindowManager类已加载');
                
                // 检查是否有实例
                if (window.stableWindowManager) {
                    addLog('✅ 窗口管理器实例已创建');
                } else {
                    addLog('⚠️ 窗口管理器实例未找到，尝试创建...');
                    try {
                        window.stableWindowManager = new StableWindowManager();
                        addLog('✅ 窗口管理器实例创建成功');
                    } catch (e) {
                        addLog('❌ 窗口管理器实例创建失败: ' + e.message);
                        updateStatus('managerStatus', '❌ 创建失败');
                        return;
                    }
                }
            } else {
                updateStatus('managerStatus', '❌ 未加载');
                addLog('❌ StableWindowManager类未找到');
                return;
            }
            
            addLog('🔧 窗口管理器测试完成');
        }

        function showSearchWindow() {
            searchWindow.style.display = 'block';
            
            // 模拟Streamly的居中逻辑
            setTimeout(() => {
                const rect = searchWindow.getBoundingClientRect();
                const headerHeight = 60;
                const availableHeight = window.innerHeight - headerHeight;
                const availableWidth = window.innerWidth;
                
                const centerX = (availableWidth - rect.width) / 2;
                const centerY = headerHeight + (availableHeight - rect.height) / 2;
                
                const constrainedX = Math.max(0, Math.min(centerX, availableWidth - rect.width));
                const constrainedY = Math.max(headerHeight, Math.min(centerY, headerHeight + availableHeight - rect.height));
                
                searchWindow.style.left = constrainedX + 'px';
                searchWindow.style.top = constrainedY + 'px';
                
                addLog(`搜索窗口显示: (${Math.round(constrainedX)}, ${Math.round(constrainedY)})`);
                
                // 检查窗口是否被增强
                setTimeout(() => {
                    const isEnhanced = searchWindow.dataset.enhanced === 'true';
                    updateStatus('windowEnhanced', isEnhanced ? '✅ 已增强' : '❌ 未增强');
                    document.getElementById('enhancementStatus').textContent = isEnhanced ? '✅ 窗口已被管理器增强' : '❌ 窗口未被增强';
                    
                    if (isEnhanced) {
                        addLog('✅ 搜索窗口已被窗口管理器增强');
                    } else {
                        addLog('❌ 搜索窗口未被增强，可能需要手动触发');
                    }
                }, 100);
            }, 50);
        }

        function hideSearchWindow() {
            searchWindow.style.display = 'none';
            addLog('搜索窗口已隐藏');
            resetStatus();
        }

        function updateStatus(elementId, text) {
            document.getElementById(elementId).textContent = text;
        }

        function resetStatus() {
            updateStatus('windowEnhanced', '未检测');
            updateStatus('touchJump', '未测试');
            updateStatus('dragFunction', '未测试');
        }

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.unshift(`[${timestamp}] ${message}`);
            if (logs.length > 8) logs.pop();
            
            document.getElementById('logOutput').innerHTML = logs.join('<br>');
        }

        // 监听触摸事件
        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('#searchResultsWindow h2')) {
                const rect = searchWindow.getBoundingClientRect();
                touchStartPos = { x: rect.left, y: rect.top };
                
                addLog('📱 触摸开始');
                updateStatus('dragFunction', '🟡 触摸中');
                
                // 延迟检查跳跃
                setTimeout(() => {
                    const newRect = searchWindow.getBoundingClientRect();
                    const deltaX = Math.abs(newRect.left - touchStartPos.x);
                    const deltaY = Math.abs(newRect.top - touchStartPos.y);
                    
                    if (deltaX > 3 || deltaY > 3) {
                        updateStatus('touchJump', `❌ 跳跃 ${deltaX}px,${deltaY}px`);
                        searchWindow.classList.add('error');
                        addLog(`❌ 检测到跳跃: ΔX=${deltaX}px, ΔY=${deltaY}px`);
                    } else {
                        updateStatus('touchJump', '✅ 无跳跃');
                        searchWindow.classList.add('success');
                        addLog('✅ 触摸开始无跳跃');
                    }
                }, 50);
            }
        });

        document.addEventListener('touchmove', function(e) {
            if (touchStartPos && searchWindow.style.display !== 'none') {
                updateStatus('dragFunction', '🔵 拖拽中');
            }
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartPos) {
                updateStatus('dragFunction', '✅ 拖拽完成');
                addLog('📱 触摸结束');
                
                setTimeout(() => {
                    searchWindow.classList.remove('success', 'error');
                }, 2000);
            }
        });

        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testWindowManager();
            }, 1000);
        });

        // 重写console.log来捕获窗口管理器日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && typeof args[0] === 'string' && args[0].includes('📱')) {
                addLog(args.join(' '));
            }
        };
    </script>
</body>
</html>
