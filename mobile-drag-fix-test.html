<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端拖拽修复测试 - Streamly</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: rgba(15, 15, 35, 0.9);
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #4fc3f7;
        }

        /* 模拟Streamly的搜索结果弹窗样式 */
        .floatingMenu {
            background: rgba(15, 15, 35, 0.95) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid rgba(79, 195, 247, 0.3) !important;
            border-radius: 20px !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
            color: #e0e0e0 !important;
            position: fixed !important;
            width: 90vw !important;
            max-width: 800px !important;
            max-height: calc(100vh - 150px) !important;
            z-index: 10000 !important;
            padding: 20px;
        }

        .floatingMenu h2 {
            color: #4fc3f7 !important;
            border-bottom: 2px solid rgba(79, 195, 247, 0.3) !important;
            padding-bottom: 10px !important;
            cursor: move !important;
            user-select: none !important;
            transition: all 0.2s ease !important;
            margin: 0 0 15px 0;
        }

        .floatingMenu h2:hover {
            background: rgba(79, 195, 247, 0.15) !important;
            border-radius: 8px 8px 0 0 !important;
            transform: translateY(-1px) !important;
        }

        .floatingMenu.being-dragged h2 {
            background: rgba(79, 195, 247, 0.2) !important;
            border-radius: 8px 8px 0 0 !important;
            box-shadow: 0 2px 10px rgba(79, 195, 247, 0.3) !important;
        }

        .floatingMenuCloseButton {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 82, 82, 0.1) !important;
            border: 1px solid rgba(255, 82, 82, 0.3) !important;
            color: #ff5252 !important;
            border-radius: 8px !important;
            padding: 5px 10px !important;
            cursor: pointer;
            transition: all 0.3s ease !important;
        }

        .test-content {
            margin-top: 80px;
            padding: 20px;
            text-align: center;
        }

        .status-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            z-index: 3000;
            max-height: 150px;
            overflow-y: auto;
        }

        .test-btn {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            padding: 12px 20px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        @media (max-width: 768px) {
            .floatingMenu h2 {
                padding: 15px 8px !important;
                -webkit-touch-callout: none !important;
                -webkit-user-select: none !important;
                -khtml-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
                user-select: none !important;
                touch-action: none !important;
            }

            .floatingMenu h2:active {
                background: rgba(79, 195, 247, 0.25) !important;
                transition: background 0.1s ease !important;
            }

            .floatingMenu.being-dragged h2 {
                background: rgba(79, 195, 247, 0.3) !important;
                box-shadow: 0 4px 20px rgba(79, 195, 247, 0.4) !important;
            }

            body.window-dragging {
                overflow: hidden !important;
                width: 100% !important;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🎬 移动端拖拽修复测试</div>
        <div id="screenInfo"></div>
    </div>

    <div class="test-content">
        <h2>📱 测试移动端弹窗拖拽修复</h2>
        <p>这个测试页面用于验证移动端搜索结果弹窗的拖拽修复效果。</p>
        
        <button class="test-btn" onclick="showTestPopup()">显示测试弹窗</button>
        <button class="test-btn" onclick="resetPopupPosition()">重置弹窗位置</button>
        
        <div style="margin-top: 30px;">
            <h3>🔧 修复内容：</h3>
            <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                <li>✅ 修复坐标系统冲突（transform vs left/top）</li>
                <li>✅ 使用getBoundingClientRect获取准确位置</li>
                <li>✅ 统一使用left/top定位方式</li>
                <li>✅ 防止弹窗跳跃到错误位置</li>
            </ul>
        </div>
    </div>

    <!-- 测试弹窗 -->
    <div id="testPopup" class="floatingMenu" style="display: none;">
        <h2 id="popupHeader">🔍 搜索结果测试弹窗</h2>
        <div class="floatingMenuCloseButton" onclick="hideTestPopup()">关闭</div>
        <div style="margin-top: 20px;">
            <p><strong>测试说明：</strong></p>
            <p>1. 用手指按住标题栏开始拖拽</p>
            <p>2. 检查弹窗是否会跳跃到错误位置</p>
            <p>3. 验证拖拽过程中弹窗是否跟随手指移动</p>
            <p>4. 确认标题栏始终在手指下方</p>
            
            <div style="margin-top: 20px; padding: 10px; background: rgba(79, 195, 247, 0.1); border-radius: 8px;">
                <strong>预期行为：</strong><br>
                弹窗应该平滑跟随手指移动，不会跳跃，标题栏保持在手指位置。
            </div>
        </div>
    </div>

    <!-- 状态面板 -->
    <div class="status-panel" id="statusPanel">
        <div><strong>📊 拖拽状态监控</strong></div>
        <div id="statusInfo">等待操作...</div>
    </div>

    <!-- 引入修复后的窗口管理器 -->
    <script src="res/js/stable-window-manager.js"></script>

    <script>
        let testPopup = document.getElementById('testPopup');
        
        function updateScreenInfo() {
            document.getElementById('screenInfo').textContent = `${window.innerWidth}x${window.innerHeight}`;
        }

        function showTestPopup() {
            testPopup.style.display = 'block';
            
            // 模拟Streamly的居中逻辑
            const rect = testPopup.getBoundingClientRect();
            const headerHeight = 60; // header高度
            const availableHeight = window.innerHeight - headerHeight;
            const availableWidth = window.innerWidth;
            
            const centerX = (availableWidth - rect.width) / 2;
            const centerY = headerHeight + (availableHeight - rect.height) / 2;
            
            const constrainedX = Math.max(0, Math.min(centerX, availableWidth - rect.width));
            const constrainedY = Math.max(headerHeight, Math.min(centerY, headerHeight + availableHeight - rect.height));
            
            testPopup.style.left = constrainedX + 'px';
            testPopup.style.top = constrainedY + 'px';
            
            updateStatus(`弹窗显示在位置: (${Math.round(constrainedX)}, ${Math.round(constrainedY)})`);
        }

        function hideTestPopup() {
            testPopup.style.display = 'none';
            updateStatus('弹窗已隐藏');
        }

        function resetPopupPosition() {
            if (testPopup.style.display !== 'none') {
                showTestPopup(); // 重新居中
                updateStatus('弹窗位置已重置');
            }
        }

        function updateStatus(message) {
            document.getElementById('statusInfo').textContent = message;
        }

        // 监听窗口大小变化
        window.addEventListener('resize', updateScreenInfo);
        
        // 初始化
        updateScreenInfo();
        
        // 监听拖拽事件（如果窗口管理器可用）
        if (window.stableWindowManager) {
            // 添加自定义事件监听
            document.addEventListener('touchstart', function(e) {
                if (e.target.closest('#testPopup h2')) {
                    updateStatus('开始触摸拖拽...');
                }
            });
            
            document.addEventListener('touchend', function(e) {
                if (testPopup.style.display !== 'none') {
                    const rect = testPopup.getBoundingClientRect();
                    updateStatus(`拖拽结束，弹窗位置: (${Math.round(rect.left)}, ${Math.round(rect.top)})`);
                }
            });
        }
    </script>
</body>
</html>
