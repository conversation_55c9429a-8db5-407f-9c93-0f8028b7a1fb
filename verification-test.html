<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试 - Streamly</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 模拟Streamly的Header */
        header {
            background: rgba(15, 15, 35, 0.9);
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
        }

        /* 模拟Streamly的Footer */
        footer {
            background: rgba(15, 15, 35, 0.9);
            height: 400px;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-top: 1px solid rgba(79, 195, 247, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-content {
            margin-top: 80px;
            margin-bottom: 420px;
            padding: 20px;
            text-align: center;
            min-height: calc(100vh - 500px);
        }

        .test-btn {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            padding: 12px 20px;
            margin: 10px;
            font-size: 14px;
        }

        .status {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 300px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            z-index: 3000;
        }

        .success { color: #4caf50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }

        .center-line {
            position: fixed;
            border: 1px dashed rgba(255, 255, 0, 0.7);
            pointer-events: none;
            z-index: 2000;
            display: none;
        }

        .center-line.horizontal {
            left: 0;
            right: 0;
            height: 1px;
        }

        .center-line.vertical {
            top: 0;
            bottom: 0;
            width: 1px;
        }
    </style>
    
    <!-- 引入Streamly的CSS -->
    <link rel="stylesheet" href="res/css/styles.css">
    <link rel="stylesheet" href="res/css/modern-theme.css">
</head>
<body>
    <header>
        <div style="font-size: 18px; font-weight: bold; color: #4fc3f7;">✅ 修复验证测试</div>
    </header>

    <div class="test-content">
        <h3>🎯 验证搜索结果弹窗修复效果</h3>
        <p>测试居中和拖拽功能是否正常工作</p>
        
        <button class="test-btn" onclick="runVerificationTest()">开始验证测试</button>
        <button class="test-btn" onclick="showSearchWindow()">显示搜索弹窗</button>
        <button class="test-btn" onclick="hideSearchWindow()">隐藏弹窗</button>
        <button class="test-btn" onclick="toggleCenterLines()">切换中心线</button>
        
        <div style="margin-top: 30px; font-size: 12px;">
            <h4>🔧 验证项目：</h4>
            <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                <li>✅ 窗口管理器正确初始化</li>
                <li>✅ 搜索弹窗正确居中显示</li>
                <li>✅ Header和Footer高度计算正确</li>
                <li>✅ 拖拽功能正常工作</li>
                <li>✅ 位置稳定不跳跃</li>
            </ul>
        </div>
    </div>

    <footer>
        <div style="text-align: center;">
            <div style="font-size: 16px; font-weight: bold; color: #4fc3f7;">Footer (400px)</div>
            <div style="font-size: 12px; margin-top: 10px;">播放列表区域</div>
        </div>
    </footer>

    <!-- 中心参考线 -->
    <div id="horizontalLine" class="center-line horizontal"></div>
    <div id="verticalLine" class="center-line vertical"></div>

    <!-- 搜索结果弹窗 -->
    <div id="searchResultsWindow" class="floatingMenu" style="display: none;">
        <h2>搜索结果:</h2>
        <div class="floatingMenuCloseButton" onclick="hideSearchWindow()">关闭</div>
        <div id="searchResults" class="floatingMenuContents">
            <div style="padding: 20px;">
                <p><strong>✅ 修复验证弹窗</strong></p>
                <p>请拖拽蓝色标题栏测试拖拽功能</p>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                    <strong>验证结果：</strong><br>
                    <span id="verificationResult">等待测试</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态面板 -->
    <div class="status">
        <div><strong>📊 验证测试状态</strong></div>
        <div style="margin-top: 10px;">
            <div><strong>窗口管理器:</strong> <span id="managerStatus">检查中...</span></div>
            <div><strong>居中功能:</strong> <span id="centerStatus">未测试</span></div>
            <div><strong>拖拽功能:</strong> <span id="dragStatus">未测试</span></div>
            <div><strong>Header高度:</strong> <span id="headerHeight">-</span></div>
            <div><strong>Footer高度:</strong> <span id="footerHeight">-</span></div>
            <div><strong>弹窗位置:</strong> <span id="windowPosition">-</span></div>
        </div>
        <div style="margin-top: 10px; font-size: 11px;">
            <div><strong>测试结果:</strong></div>
            <div id="testResults" style="max-height: 100px; overflow-y: auto; margin-top: 5px;">
                等待测试开始...
            </div>
        </div>
    </div>

    <!-- 引入jQuery -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    
    <!-- 引入窗口管理器 -->
    <script src="res/js/stable-window-manager.js"></script>

    <script>
        let searchWindow = document.getElementById('searchResultsWindow');
        let centerLinesVisible = false;
        let testResults = [];
        
        function addTestResult(message, type = 'normal') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            
            testResults.unshift(`<span class="${className}">[${timestamp}] ${message}</span>`);
            if (testResults.length > 10) testResults.pop();
            
            document.getElementById('testResults').innerHTML = testResults.join('<br>');
        }

        function runVerificationTest() {
            addTestResult('🚀 开始验证测试', 'normal');
            
            // 测试1: 检查窗口管理器
            if (typeof StableWindowManager !== 'undefined') {
                document.getElementById('managerStatus').innerHTML = '<span class="success">✅ 已加载</span>';
                addTestResult('✅ StableWindowManager类已加载', 'success');
                
                if (!window.stableWindowManager) {
                    window.stableWindowManager = new StableWindowManager();
                    addTestResult('✅ 窗口管理器实例已创建', 'success');
                }
            } else {
                document.getElementById('managerStatus').innerHTML = '<span class="error">❌ 未加载</span>';
                addTestResult('❌ StableWindowManager类未找到', 'error');
                return;
            }
            
            // 测试2: 检查高度计算
            const headerHeight = window.stableWindowManager.getHeaderHeight();
            const footerHeight = window.stableWindowManager.getFooterHeight();
            
            document.getElementById('headerHeight').textContent = headerHeight + 'px';
            document.getElementById('footerHeight').textContent = footerHeight + 'px';
            
            if (headerHeight === 70) {
                addTestResult('✅ Header高度计算正确: 70px', 'success');
            } else {
                addTestResult(`⚠️ Header高度异常: ${headerHeight}px`, 'warning');
            }
            
            if (footerHeight === 400) {
                addTestResult('✅ Footer高度计算正确: 400px', 'success');
            } else {
                addTestResult(`⚠️ Footer高度异常: ${footerHeight}px`, 'warning');
            }
            
            // 测试3: 测试居中功能
            showSearchWindow();
            
            setTimeout(() => {
                const rect = searchWindow.getBoundingClientRect();
                const expectedCenterY = 70 + (window.innerHeight - 70 - 400 - rect.height) / 2;
                const actualCenterY = rect.top;
                const deviation = Math.abs(actualCenterY - expectedCenterY);
                
                if (deviation < 10) {
                    document.getElementById('centerStatus').innerHTML = '<span class="success">✅ 正确</span>';
                    addTestResult('✅ 居中功能正常', 'success');
                } else {
                    document.getElementById('centerStatus').innerHTML = '<span class="warning">⚠️ 偏差' + Math.round(deviation) + 'px</span>';
                    addTestResult(`⚠️ 居中偏差: ${Math.round(deviation)}px`, 'warning');
                }
                
                // 测试4: 检查拖拽功能
                const isEnhanced = searchWindow.dataset.enhanced === 'true';
                if (isEnhanced) {
                    document.getElementById('dragStatus').innerHTML = '<span class="success">✅ 可用</span>';
                    addTestResult('✅ 拖拽功能已启用', 'success');
                } else {
                    document.getElementById('dragStatus').innerHTML = '<span class="error">❌ 不可用</span>';
                    addTestResult('❌ 拖拽功能未启用', 'error');
                }
                
                updateCenterLines();
                
                // 最终结果
                const allPassed = headerHeight === 70 && footerHeight === 400 && deviation < 10 && isEnhanced;
                if (allPassed) {
                    document.getElementById('verificationResult').innerHTML = '<span class="success">✅ 所有测试通过</span>';
                    addTestResult('🎉 验证测试完成 - 所有功能正常', 'success');
                } else {
                    document.getElementById('verificationResult').innerHTML = '<span class="warning">⚠️ 部分测试未通过</span>';
                    addTestResult('⚠️ 验证测试完成 - 存在问题', 'warning');
                }
            }, 200);
        }

        function showSearchWindow() {
            searchWindow.style.display = 'block';
            
            setTimeout(() => {
                if (window.stableWindowManager) {
                    window.stableWindowManager.centerWindow(searchWindow);
                }
                updateWindowPosition();
                updateCenterLines();
            }, 50);
        }

        function hideSearchWindow() {
            searchWindow.style.display = 'none';
            document.getElementById('windowPosition').textContent = '-';
        }

        function toggleCenterLines() {
            centerLinesVisible = !centerLinesVisible;
            document.getElementById('horizontalLine').style.display = centerLinesVisible ? 'block' : 'none';
            document.getElementById('verticalLine').style.display = centerLinesVisible ? 'block' : 'none';
            if (centerLinesVisible) updateCenterLines();
        }

        function updateWindowPosition() {
            if (searchWindow.style.display === 'none') return;
            
            const rect = searchWindow.getBoundingClientRect();
            document.getElementById('windowPosition').textContent = `(${Math.round(rect.left)}, ${Math.round(rect.top)})`;
        }

        function updateCenterLines() {
            if (!centerLinesVisible || searchWindow.style.display === 'none') return;
            
            const rect = searchWindow.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            document.getElementById('horizontalLine').style.top = centerY + 'px';
            document.getElementById('verticalLine').style.left = centerX + 'px';
        }

        // 监听拖拽事件
        document.addEventListener('mousedown', function(e) {
            if (e.target.closest('#searchResultsWindow h2')) {
                addTestResult('🖱️ 开始鼠标拖拽', 'normal');
            }
        });

        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('#searchResultsWindow h2')) {
                addTestResult('📱 开始触摸拖拽', 'normal');
            }
        });

        document.addEventListener('mousemove', function(e) {
            if (searchWindow.style.display !== 'none' && e.buttons === 1) {
                updateWindowPosition();
                updateCenterLines();
            }
        });

        document.addEventListener('touchmove', function(e) {
            if (searchWindow.style.display !== 'none') {
                updateWindowPosition();
                updateCenterLines();
            }
        });

        document.addEventListener('mouseup', function(e) {
            if (searchWindow.style.display !== 'none') {
                addTestResult('✅ 鼠标拖拽结束', 'success');
            }
        });

        document.addEventListener('touchend', function(e) {
            if (searchWindow.style.display !== 'none') {
                addTestResult('✅ 触摸拖拽结束', 'success');
            }
        });

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof StableWindowManager !== 'undefined' && !window.stableWindowManager) {
                    window.stableWindowManager = new StableWindowManager();
                    addTestResult('✅ 窗口管理器自动初始化', 'success');
                }
            }, 500);
        });

        // 定期更新位置
        setInterval(() => {
            if (searchWindow.style.display !== 'none') {
                updateWindowPosition();
                updateCenterLines();
            }
        }, 1000);
    </script>
</body>
</html>
