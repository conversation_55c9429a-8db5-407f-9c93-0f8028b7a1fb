<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终简化测试 - Streamly</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 模拟Streamly的Header */
        header {
            background: rgba(15, 15, 35, 0.9);
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
        }

        /* 模拟Streamly的Footer */
        footer {
            background: rgba(15, 15, 35, 0.9);
            height: 400px;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-top: 1px solid rgba(79, 195, 247, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-content {
            margin-top: 80px;
            margin-bottom: 420px;
            padding: 20px;
            text-align: center;
            min-height: calc(100vh - 500px);
        }

        .test-btn {
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            padding: 12px 20px;
            margin: 10px;
            font-size: 14px;
        }

        .status {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 250px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            z-index: 3000;
        }

        .center-guide {
            position: fixed;
            border: 2px dashed rgba(255, 255, 0, 0.7);
            pointer-events: none;
            z-index: 2000;
            display: none;
        }

        .center-guide.horizontal {
            left: 0;
            right: 0;
            height: 2px;
        }

        .center-guide.vertical {
            top: 0;
            bottom: 0;
            width: 2px;
        }
    </style>
    
    <!-- 引入Streamly的CSS -->
    <link rel="stylesheet" href="res/css/styles.css">
    <link rel="stylesheet" href="res/css/modern-theme.css">
</head>
<body>
    <header>
        <div style="font-size: 18px; font-weight: bold; color: #4fc3f7;">✅ 最终简化测试</div>
    </header>

    <div class="test-content">
        <h3>🎯 测试搜索结果弹窗居中和拖拽</h3>
        <p>验证简化修复后的效果</p>
        
        <button class="test-btn" onclick="showSearchWindow()">显示搜索结果弹窗</button>
        <button class="test-btn" onclick="hideSearchWindow()">隐藏弹窗</button>
        <button class="test-btn" onclick="toggleGuides()">切换参考线</button>
        
        <div style="margin-top: 30px; font-size: 12px;">
            <h4>🔧 简化修复：</h4>
            <ul style="text-align: left; max-width: 400px; margin: 20px auto;">
                <li>✅ 移除复杂的标记逻辑</li>
                <li>✅ 简化Footer高度计算</li>
                <li>✅ 统一居中逻辑</li>
                <li>✅ 确保拖拽功能正常</li>
            </ul>
            
            <div style="margin-top: 20px; padding: 15px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                <strong>预期效果：</strong><br>
                • 弹窗正确居中在Header和Footer之间<br>
                • 位置稳定，不跳跃<br>
                • 可以正常拖拽移动<br>
                • 拖拽平滑跟随鼠标/手指
            </div>
        </div>
    </div>

    <footer>
        <div style="text-align: center;">
            <div style="font-size: 16px; font-weight: bold; color: #4fc3f7;">Footer (400px)</div>
            <div style="font-size: 12px; margin-top: 10px;">播放列表区域</div>
        </div>
    </footer>

    <!-- 居中参考线 -->
    <div id="horizontalGuide" class="center-guide horizontal"></div>
    <div id="verticalGuide" class="center-guide vertical"></div>

    <!-- 使用Streamly原始的搜索结果窗口结构 -->
    <div id="searchResultsWindow" class="floatingMenu" style="display: none;">
        <h2>搜索结果:</h2>
        <div class="floatingMenuCloseButton" onclick="hideSearchWindow()">关闭</div>
        <div id="searchStatus" style="padding: 10px; font-style: italic; color: #666; display: none;"></div>
        <div id="searchResults" class="floatingMenuContents">
            <div style="padding: 20px;">
                <p><strong>✅ 简化修复测试弹窗</strong></p>
                <p>请拖拽蓝色标题栏测试移动功能</p>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                    <strong>测试项目：</strong><br>
                    • 居中位置是否正确<br>
                    • 是否可以拖拽移动<br>
                    • 拖拽是否平滑<br>
                    • 位置是否稳定
                </div>
            </div>
        </div>
    </div>

    <!-- 状态面板 -->
    <div class="status">
        <div><strong>📊 测试状态</strong></div>
        <div style="margin-top: 10px;">
            <div><strong>弹窗状态:</strong> <span id="windowStatus">未显示</span></div>
            <div><strong>居中状态:</strong> <span id="centerStatus">未测试</span></div>
            <div><strong>拖拽功能:</strong> <span id="dragStatus">未测试</span></div>
            <div><strong>位置:</strong> <span id="position">-</span></div>
        </div>
    </div>

    <!-- 引入jQuery（Streamly依赖） -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    
    <!-- 引入窗口管理器 -->
    <script src="res/js/stable-window-manager.js"></script>

    <script>
        let searchWindow = document.getElementById('searchResultsWindow');
        let guidesVisible = false;
        
        function showSearchWindow() {
            searchWindow.style.display = 'block';
            document.getElementById('windowStatus').textContent = '✅ 已显示';
            
            // 模拟搜索逻辑的居中
            setTimeout(() => {
                if (window.stableWindowManager) {
                    window.stableWindowManager.centerWindow(searchWindow);
                    document.getElementById('centerStatus').textContent = '✅ 窗口管理器居中';
                } else {
                    // 备用居中
                    const headerHeight = 70;
                    const footerHeight = 400;
                    const availableHeight = window.innerHeight - headerHeight - footerHeight;
                    const rect = searchWindow.getBoundingClientRect();
                    
                    const centerX = (window.innerWidth - rect.width) / 2;
                    const centerY = headerHeight + (availableHeight - rect.height) / 2;
                    
                    searchWindow.style.left = Math.max(0, centerX) + 'px';
                    searchWindow.style.top = Math.max(headerHeight, centerY) + 'px';
                    
                    document.getElementById('centerStatus').textContent = '✅ 备用居中';
                }
                
                updatePosition();
                updateGuides();
            }, 10);
        }

        function hideSearchWindow() {
            searchWindow.style.display = 'none';
            document.getElementById('windowStatus').textContent = '❌ 未显示';
            document.getElementById('centerStatus').textContent = '未测试';
            document.getElementById('dragStatus').textContent = '未测试';
            document.getElementById('position').textContent = '-';
        }

        function toggleGuides() {
            guidesVisible = !guidesVisible;
            document.getElementById('horizontalGuide').style.display = guidesVisible ? 'block' : 'none';
            document.getElementById('verticalGuide').style.display = guidesVisible ? 'block' : 'none';
            if (guidesVisible) updateGuides();
        }

        function updatePosition() {
            if (searchWindow.style.display === 'none') return;
            
            const rect = searchWindow.getBoundingClientRect();
            document.getElementById('position').textContent = `(${Math.round(rect.left)}, ${Math.round(rect.top)})`;
        }

        function updateGuides() {
            if (!guidesVisible || searchWindow.style.display === 'none') return;
            
            const rect = searchWindow.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            document.getElementById('horizontalGuide').style.top = centerY + 'px';
            document.getElementById('verticalGuide').style.left = centerX + 'px';
        }

        // 监听拖拽事件
        document.addEventListener('mousedown', function(e) {
            if (e.target.closest('#searchResultsWindow h2')) {
                document.getElementById('dragStatus').textContent = '🖱️ 鼠标拖拽中';
            }
        });

        document.addEventListener('touchstart', function(e) {
            if (e.target.closest('#searchResultsWindow h2')) {
                document.getElementById('dragStatus').textContent = '📱 触摸拖拽中';
            }
        });

        document.addEventListener('mousemove', function(e) {
            if (searchWindow.style.display !== 'none' && e.buttons === 1) {
                updatePosition();
                updateGuides();
            }
        });

        document.addEventListener('touchmove', function(e) {
            if (searchWindow.style.display !== 'none') {
                updatePosition();
                updateGuides();
            }
        });

        document.addEventListener('mouseup', function(e) {
            if (document.getElementById('dragStatus').textContent.includes('鼠标拖拽')) {
                document.getElementById('dragStatus').textContent = '✅ 鼠标拖拽完成';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (document.getElementById('dragStatus').textContent.includes('触摸拖拽')) {
                document.getElementById('dragStatus').textContent = '✅ 触摸拖拽完成';
            }
        });

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof StableWindowManager !== 'undefined' && !window.stableWindowManager) {
                    window.stableWindowManager = new StableWindowManager();
                    console.log('✅ 窗口管理器已创建');
                }
            }, 500);
        });

        // 定期更新位置
        setInterval(() => {
            if (searchWindow.style.display !== 'none') {
                updatePosition();
                updateGuides();
            }
        }, 1000);
    </script>
</body>
</html>
