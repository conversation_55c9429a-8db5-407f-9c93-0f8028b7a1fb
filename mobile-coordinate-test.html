<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端坐标测试 - Streamly</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: rgba(15, 15, 35, 0.9);
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #4fc3f7;
        }

        .test-popup {
            position: fixed;
            width: 300px;
            height: 200px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            z-index: 2000;
            left: 50px;
            top: 100px;
        }

        .popup-header {
            background: rgba(79, 195, 247, 0.1);
            padding: 10px 15px;
            border-radius: 15px 15px 0 0;
            border-bottom: 1px solid rgba(79, 195, 247, 0.3);
            cursor: move;
            user-select: none;
            font-weight: bold;
            color: #4fc3f7;
        }

        .popup-content {
            padding: 15px;
            font-size: 14px;
            line-height: 1.4;
        }

        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(15, 15, 35, 0.95);
            border: 1px solid rgba(79, 195, 247, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            z-index: 3000;
            max-height: 200px;
            overflow-y: auto;
        }

        .coordinate-display {
            margin-top: 80px;
            padding: 20px;
            text-align: center;
        }

        .coord-item {
            margin: 10px 0;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px;
            flex-wrap: wrap;
        }

        .test-btn {
            flex: 1;
            min-width: 120px;
            padding: 10px;
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        .test-btn:active {
            transform: translateY(0);
        }

        @media (max-width: 480px) {
            .test-popup {
                width: 280px;
                left: 20px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .test-btn {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🎬 Streamly 坐标测试</div>
        <div id="screenSize"></div>
    </div>

    <div class="coordinate-display">
        <h2>📱 移动端坐标边界分析</h2>
        
        <div class="coord-item">
            <strong>浏览器窗口尺寸:</strong> <span id="windowSize"></span>
        </div>
        
        <div class="coord-item">
            <strong>可视区域宽度:</strong> <span id="viewportWidth"></span>
        </div>
        
        <div class="coord-item">
            <strong>页面可视范围左边界:</strong> <span id="leftBoundary">0px</span>
        </div>
        
        <div class="coord-item">
            <strong>页面可视范围右边界:</strong> <span id="rightBoundary"></span>
        </div>
        
        <div class="coord-item">
            <strong>Header高度:</strong> <span id="headerHeight"></span>
        </div>
        
        <div class="coord-item">
            <strong>弹窗当前位置:</strong> <span id="popupPosition"></span>
        </div>
        
        <div class="coord-item">
            <strong>弹窗可移动左边界:</strong> <span id="popupLeftBoundary"></span>
        </div>
        
        <div class="coord-item">
            <strong>弹窗可移动右边界:</strong> <span id="popupRightBoundary"></span>
        </div>
        
        <div class="coord-item">
            <strong>边界重合状态:</strong> <span id="boundaryMatch"></span>
        </div>
    </div>

    <div class="button-group">
        <button class="test-btn" onclick="movePopupToLeft()">移动到左边界</button>
        <button class="test-btn" onclick="movePopupToRight()">移动到右边界</button>
        <button class="test-btn" onclick="movePopupToCenter()">移动到中心</button>
        <button class="test-btn" onclick="testDragBoundaries()">测试拖拽边界</button>
    </div>

    <!-- 测试弹窗 -->
    <div class="test-popup" id="testPopup">
        <div class="popup-header" id="popupHeader">
            🔍 搜索结果测试弹窗
        </div>
        <div class="popup-content">
            这是一个模拟的搜索结果弹窗，用于测试移动端的拖拽边界和坐标范围。
            <br><br>
            请尝试拖拽此弹窗来测试边界限制。
        </div>
    </div>

    <!-- 信息面板 -->
    <div class="info-panel" id="infoPanel">
        <div><strong>📊 实时坐标信息</strong></div>
        <div id="realTimeInfo">等待触摸或鼠标操作...</div>
    </div>

    <script>
        let popup = document.getElementById('testPopup');
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let touchStartInfo = null;

        // 初始化
        function init() {
            updateCoordinateInfo();
            setupDragEvents();
            
            // 定期更新信息
            setInterval(updateCoordinateInfo, 1000);
        }

        // 更新坐标信息
        function updateCoordinateInfo() {
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const header = document.querySelector('.header');
            const headerHeight = header ? header.offsetHeight : 0;
            
            // 页面可视范围
            const leftBoundary = 0;
            const rightBoundary = windowWidth;
            
            // 弹窗信息
            const popupRect = popup.getBoundingClientRect();
            const popupWidth = popupRect.width;
            
            // 弹窗可移动范围（考虑最小可见宽度150px）
            const minVisibleWidth = 150;
            const popupLeftBoundary = 0;
            const popupRightBoundary = windowWidth - minVisibleWidth;
            
            // 检查边界是否重合
            const boundariesMatch = (leftBoundary === popupLeftBoundary && rightBoundary === popupRightBoundary);
            
            // 更新显示
            document.getElementById('windowSize').textContent = `${windowWidth} x ${windowHeight}`;
            document.getElementById('viewportWidth').textContent = `${windowWidth}px`;
            document.getElementById('rightBoundary').textContent = `${rightBoundary}px`;
            document.getElementById('headerHeight').textContent = `${headerHeight}px`;
            document.getElementById('popupPosition').textContent = `left: ${Math.round(popupRect.left)}px, top: ${Math.round(popupRect.top)}px`;
            document.getElementById('popupLeftBoundary').textContent = `${popupLeftBoundary}px`;
            document.getElementById('popupRightBoundary').textContent = `${popupRightBoundary}px`;
            document.getElementById('boundaryMatch').textContent = boundariesMatch ? '✅ 完全重合' : '❌ 不重合';
            document.getElementById('screenSize').textContent = `${windowWidth}x${windowHeight}`;
        }

        // 设置拖拽事件
        function setupDragEvents() {
            const header = document.getElementById('popupHeader');
            
            // 鼠标事件
            header.addEventListener('mousedown', startMouseDrag);
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', endDrag);
            
            // 触摸事件
            header.addEventListener('touchstart', startTouchDrag, { passive: false });
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', endDrag);
        }

        // 开始鼠标拖拽
        function startMouseDrag(e) {
            isDragging = true;
            const rect = popup.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            
            updateRealTimeInfo(`🖱️ 开始鼠标拖拽 - 偏移: (${dragOffset.x}, ${dragOffset.y})`);
            e.preventDefault();
        }

        // 开始触摸拖拽
        function startTouchDrag(e) {
            const touch = e.touches[0];
            if (!touch) return;
            
            const rect = popup.getBoundingClientRect();
            touchStartInfo = {
                startX: touch.clientX,
                startY: touch.clientY,
                initialLeft: rect.left,
                initialTop: rect.top,
                isDragging: false
            };
            
            updateRealTimeInfo(`📱 触摸开始 - 位置: (${touch.clientX}, ${touch.clientY})`);
            e.preventDefault();
        }

        // 处理鼠标移动
        function handleMouseMove(e) {
            if (!isDragging) return;
            
            const newX = e.clientX - dragOffset.x;
            const newY = e.clientY - dragOffset.y;
            
            const constrainedPos = constrainPosition(newX, newY);
            popup.style.left = constrainedPos.x + 'px';
            popup.style.top = constrainedPos.y + 'px';
            
            updateRealTimeInfo(`🖱️ 鼠标拖拽 - 原始: (${newX}, ${newY}), 限制后: (${constrainedPos.x}, ${constrainedPos.y})`);
            updateCoordinateInfo();
        }

        // 处理触摸移动
        function handleTouchMove(e) {
            if (!touchStartInfo) return;
            
            const touch = e.touches[0];
            if (!touch) return;
            
            if (!touchStartInfo.isDragging) {
                const deltaX = touch.clientX - touchStartInfo.startX;
                const deltaY = touch.clientY - touchStartInfo.startY;
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                
                if (distance > 10) {
                    touchStartInfo.isDragging = true;
                    updateRealTimeInfo(`📱 激活触摸拖拽 - 移动距离: ${Math.round(distance)}px`);
                }
            }
            
            if (touchStartInfo.isDragging) {
                const deltaX = touch.clientX - touchStartInfo.startX;
                const deltaY = touch.clientY - touchStartInfo.startY;
                
                const newX = touchStartInfo.initialLeft + deltaX;
                const newY = touchStartInfo.initialTop + deltaY;
                
                const constrainedPos = constrainPosition(newX, newY);
                popup.style.left = constrainedPos.x + 'px';
                popup.style.top = constrainedPos.y + 'px';
                
                updateRealTimeInfo(`📱 触摸拖拽 - 原始: (${newX}, ${newY}), 限制后: (${constrainedPos.x}, ${constrainedPos.y})`);
                updateCoordinateInfo();
            }
            
            e.preventDefault();
        }

        // 约束位置
        function constrainPosition(x, y) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const minVisibleWidth = 150;
            const titleBarHeight = 50;
            
            const minX = 0;
            const maxX = window.innerWidth - minVisibleWidth;
            const minY = headerHeight;
            const maxY = window.innerHeight - titleBarHeight;
            
            return {
                x: Math.max(minX, Math.min(x, maxX)),
                y: Math.max(minY, Math.min(y, maxY))
            };
        }

        // 结束拖拽
        function endDrag() {
            isDragging = false;
            touchStartInfo = null;
            updateRealTimeInfo('拖拽结束');
            updateCoordinateInfo();
        }

        // 更新实时信息
        function updateRealTimeInfo(message) {
            document.getElementById('realTimeInfo').textContent = message;
        }

        // 测试函数
        function movePopupToLeft() {
            popup.style.left = '0px';
            popup.style.top = '100px';
            updateCoordinateInfo();
            updateRealTimeInfo('弹窗移动到左边界');
        }

        function movePopupToRight() {
            const maxX = window.innerWidth - 150; // 最小可见宽度
            popup.style.left = maxX + 'px';
            popup.style.top = '100px';
            updateCoordinateInfo();
            updateRealTimeInfo('弹窗移动到右边界');
        }

        function movePopupToCenter() {
            const centerX = (window.innerWidth - popup.offsetWidth) / 2;
            popup.style.left = centerX + 'px';
            popup.style.top = '100px';
            updateCoordinateInfo();
            updateRealTimeInfo('弹窗移动到中心');
        }

        function testDragBoundaries() {
            updateRealTimeInfo('请尝试拖拽弹窗到屏幕边缘测试边界限制');
        }

        // 窗口大小改变时更新信息
        window.addEventListener('resize', updateCoordinateInfo);

        // 初始化
        init();
    </script>
</body>
</html>
